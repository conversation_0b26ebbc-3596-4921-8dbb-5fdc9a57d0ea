<script lang="ts" setup>
import { reactive, onMounted } from "vue";
import { useRoute } from 'vue-router';
definePageMeta({
  middleware: ["check-browser", "unauth"],
});
const route = useRoute();
const teacherID = route.params.id;
onMounted(async () => {//進入畫面時
  console.log(teacherID);//現在路由的ID部分 有可能是teacherID或是teacherWebIndex
});

</script>
<template>
  <TeacherDetailPage :teacherID=teacherID :isEdit=false />
</template>