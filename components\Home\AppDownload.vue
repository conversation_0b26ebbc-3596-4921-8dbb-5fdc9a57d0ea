<script setup lang="ts">
definePageMeta({
  middleware: ["unauth"],
});
const googlePlayUrl = "https://play.google.com/store/apps/details?id=lab.italkutalk";
const appStoreUrl = "https://apps.apple.com/tw/app/italkutalk/id1263409577";
const props = defineProps({
  language: {//圖片語言
    type: String,
    required: true,
  },
});
</script>

<template>
  <div class="appIntro-page">
    <!-- APP 下載卡片 -->
    <el-card class="appDownload-card">
      <div class="appDownload-content">
        <div class="text-content">
          <span>{{ $t('home.appDownloadA1') }}</span>
          <span>{{ $t('home.appDownloadA2') }}</span>
          <span>{{ $t('home.appDownloadA2B') }}</span>
          <span>{{ $t('home.appDownloadA3') }}</span>
          <span>{{ $t('home.appDownloadB0') }}</span>
          <span>{{ $t('home.appDownloadB1') }}</span>
          <span>{{ $t('home.appDownloadB2') }}</span>
          <span>{{ $t('home.appDownloadB3') }}</span>
          <div class="store-section">
            <a :href="googlePlayUrl" target="_blank">
              <img src="..\..\public\googlePlay.png" alt="Google Play" class="google-play-logo">
            </a>
            <a :href="appStoreUrl" target="_blank">
              <img src="..\..\public\appStore.png" alt="App Store" class="app-store-logo">
            </a>
          </div>
        </div>
        <div class="image-content">
          <a :href="googlePlayUrl" target="_blank">
            <img :src="`https://itut-website.s3.ap-northeast-1.amazonaws.com/public/appIntro/over1M_${props.language}.jpg`" alt="GooglePlayPage" class="app-logo" />
          </a>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.itutLogo {
  max-height: 90px;
  max-width: 442px;
  max-width: 100%;
  margin-bottom: 40px;
}

.appIntro-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px 20px 0 20px;
}

.appDownload-card {
  padding: 20px;
  background: linear-gradient(to bottom, #ffffff, #daecf9);
  /* 背景漸層 */
}

.appDownload-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
}

.text-content {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  color: #27465e;
  /* 文字內文顏色 */
  font-weight: bold;
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.5;
  /* 行距 */
  font-size: 22px;
  /* 字體大小 */

}

.text-content span {
  margin-left: 12px;
}

.image-content {
  max-width: 535px;
  max-height: 535px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.app-logo {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  aspect-ratio: 1 / 1;
  /* 保持比例為 1:1 */
  object-fit: contain;
  /* 確保圖片在容器內完整顯示 */
}

.store-section {
  display: flex;
  justify-content: flex-start;
  gap: 0px;
  margin-bottom: 10px;
}

.google-play-logo {
  height: 70px;
  width: auto;
  margin-top: 50px;
}

.app-store-logo {
  height: 50px;
  width: auto;
  margin-top: 59px;
}

@media (max-width: 500px) {

  .image-content {
    justify-content: center;
    width: 100%;
    text-align: center;
  }

  .appIntro-page {
    padding: 0px;
  }

  .store-section[data-v-0ba926e9] {
    gap: 0px;
    margin-bottom: 10px;
  }
}

@media (max-width: 800px) {
  .appDownload-content {
    flex-direction: column;
  }

  .app-logo {
    aspect-ratio: initial;
  }

  .text-content,
  .store-section,
  .itut-logo,
  .image-content {
    justify-content: center;
    width: 100%;
    text-align: center;
  }

  .text-content span {
    margin-left: 0px;
  }

}

@media (max-width: 1000px) {
  .el-card {
    --el-card-border-color: var(--el-border-color-light);
    --el-card-border-radius: 4px;
    --el-card-padding: 0px;
    --el-card-bg-color: var(--el-fill-color-blank);
  }

  .google-play-logo {
    margin-top: 0px;
  }

  .app-store-logo {
    margin-top: 9px;
  }
}

@media (min-width: 1500px) {
  .appIntro-page {
    padding-right: 100px;
  }
}

@media (min-width: 1600px) {
  .appIntro-page {
    padding-right: 200px;
  }
}

@media (min-width: 1700px) {
  .appIntro-page {
    padding-right: 300px;
  }
}
</style>