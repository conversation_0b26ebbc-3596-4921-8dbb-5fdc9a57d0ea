<script setup>
import queryString from "query-string";
import { globalStore } from "../../stores/global";
import { reactive, onMounted } from "vue";
import { useI18n } from 'vue-i18n';
import { GoogleLogin } from 'vue3-google-login';

const config = useRuntimeConfig();
const facebookLoginUrl = ref("");
const loading = ref(false);
const isRegister = ref(false);//是否是註冊模式
const emailFrom = ref(false);//是否開啟email表格
const waitVerify = ref(false);//等待驗證
const resetPassword = ref(false);//更改密碼
const { t } = useI18n();
watchEffect(() => {
  useHead({
    title: `${t('menu.login')} - ${t('home.title')}`,
  });
});
let store = globalStore();
const loginForm = reactive({ email: "", password: "", code: "" });
const navigateToTerms = () => {
  navigateTo("/terms");
};

function handleEailClick() {// email註冊/登入
  emailFrom.value = !(emailFrom.value);
  waitVerify.value = false;
};
function handleRegisterClick() {// email註冊/登入
  isRegister.value = !(isRegister.value)
};

const onSubmit = async function (e) {
  console.log(isRegister.value);
  console.log(waitVerify.value);
  console.log(resetPassword.value);
  e.preventDefault();
  if (loginForm.email === "" || loginForm.password === "" || loginForm.password.length < 6)
    return;//沒有輸入東西 不要動作
  if (!waitVerify.value)//第一次按下按鈕 不管啥狀態 都先檢查一遍是否有帳號 密碼是否正確
  {
    const checkResult = await store.signInCheck(loginForm.email.trim(), loginForm.password)
    if (checkResult.isExisted)//有帳號
    {
      if (checkResult.passwordCorrect)//密碼正確
      {//登入
        loading.value = true;
        await store.signInAndSaveCache(loginForm.email.trim(), loginForm.password)
          .then(() => {
          })
          .finally(() => {
            loading.value = false;
            let user = store.getCurrentUser;
            if (user) {//成功
              store.setCurrentRoute("/event");
              navigateTo(store.getCurrentRoute);
            }
            else {//失敗
              store.setCurrentRoute("/login");
              navigateTo(store.getCurrentRoute);
            }
          });
      }
      else {//改密碼 
        await store.forgot(loginForm.email.trim())
          .then(() => {
          })
          .finally(() => {
            resetPassword.value = true;
            waitVerify.value = true;
          });
      }
    }
    else {//註冊
      await store.register(loginForm.email.trim(), loginForm.password)
        .then(() => {
        })
        .finally(() => {
          waitVerify.value = true;
        });
    }
  }
  else//驗證模式
  {
    if (resetPassword.value)//改密碼後的登入
    {
      if (loginForm.code.length != 6)
        return;//沒有輸入東西 不要動作
      loading.value = true;
      await store.resetPassword(loginForm.email.trim(), loginForm.password, loginForm.code)
        .then(() => {
          resetPassword.value = false;
        })
        .finally(() => {
          loading.value = false;
          let user = store.getCurrentUser;
          if (user) {//成功
            store.setCurrentRoute("/event");
            navigateTo(store.getCurrentRoute);
          }
          else {//失敗
            store.setCurrentRoute("/login");
            navigateTo(store.getCurrentRoute);
          }
        });
    }
    else//註冊後的登入
    {
      if (loginForm.code.length != 6)
        return;//沒有輸入東西 不要動作
      loading.value = true;
      await store.verify(loginForm.email.trim(), loginForm.code)
        .then(() => {
        })
        .finally(() => {
          loading.value = false;
          let user = store.getCurrentUser;
          if (user) {//成功
            store.setCurrentRoute("/event");
            navigateTo(store.getCurrentRoute);
          }
          else {//失敗
            store.setCurrentRoute("/login");
            navigateTo(store.getCurrentRoute);
          }
        });
    }
  }
};

const onAppleSigninSuccess = async (data) => {
  console.log(data);
  await store
    .signInWithApple(data.userData.sub, data.authorization.code, data.userData.email)
    .then(() => {
      //navigateTo(store.getRedirect ?? "/home");
      //store.setRedirect(undefined);
    })
    .finally(() => {
      loading.value = false;
      let user = store.getCurrentUser;
      if (user) {//成功
        store.setCurrentRoute("/event");
        console.log(store.getCurrentRoute);
        navigateTo(store.getCurrentRoute);
      }
      else {//失敗
        store.setCurrentRoute("/login");
        console.log(store.getCurrentRoute);
        navigateTo(store.getCurrentRoute);
      }
    });
};

const onFailure = async (error) => {
  console.log(error);
};

const loginWithGoogle = async(response) => {
  console.log("Handle the response", response)
  console.log(response);
  await store
    .signInWithGoogle(response.code)
    .then(() => {
      //navigateTo(store.getRedirect ?? "/home");
      //store.setRedirect(undefined);
    })
    .finally(() => {
      loading.value = false;
      let user = store.getCurrentUser;
      if (user) {//成功
        store.setCurrentRoute("/event");
        console.log(store.getCurrentRoute);
        navigateTo(store.getCurrentRoute);
      }
      else {//失敗
        store.setCurrentRoute("/login");
        console.log(store.getCurrentRoute);
        navigateTo(store.getCurrentRoute);
      }
    });
}

onMounted(() => {
  const FB_DIALOG_LINK = "https://www.facebook.com/v17.0/dialog/oauth";
  const params = queryString.stringify({
    client_id: config.public.fbClientId,
    redirect_uri: `${config.public.webBaseUrl}/login/facebook`,
    scope: ["public_profile", "email"].join(","),
  });
  facebookLoginUrl.value = `${FB_DIALOG_LINK}?${params}`;

  const route = useRoute();
  //store.setRedirect(route.query.redirect);
  if (store.getCurrentUser) {
    navigateTo(store.getRedirect ?? "/event");//原本去到/home 現在改成直接進event
    store.setRedirect(undefined);
  }
});

definePageMeta({
  layout: false,
  middleware: ["check-browser"],
});
</script>

<template>
  <NuxtLayout name="unauthorized">
    <div style="height: 80px" />
    <div class="center-login">
      <img src="/itut-logo-2.png" style="width: 250px" />
    </div>
    <div style="height: 30px" />
    <div class="center-login">
      <el-card class="center-login" v-loading="loading">
        <div class="center-container">
          <span v-if="!isRegister" class="loginTitle">{{ $t("menu.login") }}</span>
          <span v-if="isRegister" class="loginTitle">{{ $t("menu.register") }}</span>
        </div>
        <div class="btnContainer">
          <a :href="facebookLoginUrl">
            <img src="..\..\public\fb.jpg" alt="Sign in with Facebook" class="loginBtn">
          </a>
          <div class="spacer"></div>
          <vue-apple-login class="third-party-button" :onSuccess="onAppleSigninSuccess" data-mode="logo-only"
            :onFailure="onFailure"></vue-apple-login>
          <div class="spacer"></div>
          <img src="..\..\public\email.jpg" alt="Sign in with Email" class="loginBtn2" @click="handleEailClick">
          <ClientOnly>
            <GoogleLogin :callback="loginWithGoogle" prompt>
              <img src="..\..\public\google.jpg" alt="Sign in with Google" class="loginBtn2">
            </GoogleLogin>
          </ClientOnly>
        </div>
        <div class="center-container">
          <div class="inline-loginPage-container">
            <span v-if="!isRegister" class="text">{{ $t("menu.notRegister") }}?</span>
            <span v-if="!isRegister" class="clickable" @click="handleRegisterClick">{{ $t("menu.goRegister") }}!</span>
            <span v-if="isRegister" class="text">{{ $t("menu.registed") }}?</span>
            <span v-if="isRegister" class="clickable" @click="handleRegisterClick">{{ $t("menu.goLogin") }}!</span>
          </div>
        </div>
        <el-divider v-if="emailFrom" />
        <el-form class="center-form" :model="loginForm" @submit="onSubmit" v-if="emailFrom">
          <el-space direction="vertical">
            <el-input style="width: 260px" :disabled=waitVerify v-model="loginForm.email" @keyup.enter="onSubmit"
              placeholder="Email" />
            <el-input style="width: 260px" :disabled=waitVerify v-model="loginForm.password" @keyup.enter="onSubmit"
              type="password" :placeholder="$t('menu.password')" />
            <span v-if="isRegister" class="register-text">{{ $t("menu.emailRule") }}</span>
            <el-button type="primary" v-if="isRegister" :disabled=waitVerify @click="onSubmit">{{
        $t("menu.sendVerifyCode")
      }}</el-button>
            <el-button type="primary" v-if="!isRegister" @click="onSubmit">{{ $t("menu.login") }}</el-button>
            <span v-if="waitVerify" class="register-text">{{ $t("menu.goEmail") }}{{ loginForm.email }}</span>
            <span v-if="waitVerify" class="register-text">{{ $t("menu.emailNotReceive") }}</span>
            <el-input style="width: 260px" v-if="waitVerify" v-model="loginForm.code" @keyup.enter="onSubmit"
              :placeholder="$t('menu.code')" />
            <el-button type="primary" v-if="waitVerify" @click="onSubmit">{{ $t("menu.verify") }}</el-button>
          </el-space>
        </el-form>
        <div class="center-container">
          <div class="terms">
            <el-link type="info" @click="navigateToTerms" target="_blank">{{ $t('account.terms') }}</el-link>
          </div>
        </div>
      </el-card>
    </div>
  </NuxtLayout>
</template>

<style>
.center-login {
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-form {
  display: flex;
  flex-direction: column;
}

.center-login el-card {
  min-width: 250px;
  padding: 20px;
}

.center-login el-card__body {
  width: 100%;
  padding: 0;
}

.login-dialog {
  max-width: 300px;
  width: 100%;
}

.login-dialog header {
  padding: 0px;
  /* 覆蓋 Element Plus 預設的 max-width */
}

.login-dialog :deep(.el-dialog + .el-dialog) {
  --el-dialog-width: 100% !important;
  /* 覆蓋 Element Plus 預設的 max-width */
}

.loginTitle {
  font-weight: bold;
  font-size: 24px;
  color: #000000;
  margin-bottom: 20px;
}

.btnContainer {
  display: flex;
  align-items: flex-start;
}

.spacer {
  width: 10px;
}

.third-party-button {
  height: 80px;
  width: 80px;
  cursor: pointer;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-direction: column;
}

.terms {
  margin-top: 5px;
}

#appleid-signin>div {
  width: auto !important;
  height: auto !important;
  min-width: 30px !important;
  max-width: none !important;
  min-height: 30px !important;
  max-height: none !important;
}

.loginBtn {
  max-width: 80px;
  cursor: pointer;
  border-radius: 15px;
}

.loginBtn2 {
  max-width: 80px;
  cursor: pointer;
  border-radius: 15px;
  margin-right:  10px;
}

.inline-loginPage-container {
  margin-top: 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
  max-width: 260px;
}

.inline-loginPage-container .text,
.inline-loginPage-container .clickable {
  display: block;
  /* 讓每個 span 元素都在新行顯示 */
  /* 設置文字大小 */
  white-space: nowrap;
  /* 防止自動換行 */
}

.clickable {
  cursor: pointer;
  color: #2766a9;
  /* 根據需要設置樣式 */
  margin-left: 5px;
  /* 為間距添加樣式 */
}

.register-text {
  font-size: 14px;
  /* 設置文字大小 */
  width: 260px;
  /* 設置寬度 */
  word-wrap: break-word;
  /* 當單詞超過寬度時換行 */
  word-break: break-all;
  /* 當單詞超過寬度時換行 */
  white-space: normal;
  /* 確保文字會換行 */
}
</style>
