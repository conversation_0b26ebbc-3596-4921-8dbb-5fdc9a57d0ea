version: "3"
services:
  nginx:
    image: nginx:stable
    container_name: nginx
    volumes:
      - ./nginx.conf:/etc/nginx/templates/default.conf.template
    environment:
      - API_URL=${API_URL}
    restart: always
    depends_on:
      - itut-web
    ports:
      - ${PORT}:80
  itut-web:
    image: 052397341544.dkr.ecr.ap-northeast-1.amazonaws.com/itut-nuxt-web:${VERSION}
    container_name: itut-web
    restart: always
