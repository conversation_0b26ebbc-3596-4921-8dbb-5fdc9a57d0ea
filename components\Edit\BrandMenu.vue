<template>
  <el-dropdown @command="handleCommand" size="large">
    <span class="el-dropdown-link">
      <img src="../../public/_icons_/dropdown-icon.svg" alt="Custom Icon" style="width: 24px; height: 24px;" />
    </span>
    <template #dropdown>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item  class="dropdown-item" command="editBrand">{{ $t('brand.edit') }}</el-dropdown-item>
        <el-dropdown-item  class="dropdown-item" command="addTeacher">{{ $t('brand.addTeacher') }}</el-dropdown-item>
        <el-dropdown-item  class="dropdown-item" command="addEvent">{{ $t('brand.addEvent') }}</el-dropdown-item>
        <el-dropdown-item  class="dropdown-item" command="addPastEvent">{{ $t('brand.addPastEvent') }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <EditDialog :type=type :brandDetail="props.brandDetail" :visible=isDialogVisible @update:visible="isDialogVisible = $event"></EditDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BrandDetail } from "~~/models/brand";
const props = defineProps<{
  brandDetail: BrandDetail;//直接拿整個brandDetail
}>();

const isDialogVisible = ref(false);
const type = ref(1);
const handleCommand = (command: string) => {
  switch (command) {
    case 'editBrand':
      editData();
      
      break;
    case 'addTeacher':
      addTeacher();
      break;
    case 'addEvent':
      addActivity();
      break;
    case 'addPastEvent':
      addPastActivity();
      break;
  }
};

const editData = () => {
  console.log('編輯資料');
  type.value = 1;
  isDialogVisible.value = true;
};

const addTeacher = () => {
  console.log('新增教師');
  type.value = 2;
  isDialogVisible.value = true;
};

const addActivity = () => {
  console.log('新增活動');
  type.value = 3;
  isDialogVisible.value = true;
};

const addPastActivity = () => {
  console.log('新增過往活動');
  type.value = 4;
  isDialogVisible.value = true;
};
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
}
::v-deep .el-dropdown-menu__item {
  font-size: 16px; /* 調整字號 */
}
</style>