<script lang="ts" setup>
definePageMeta({
  middleware: ["check-browser", "unauth"],
});
import { useI18n } from "vue-i18n";
const { t } = useI18n()
const selectTag = ref('');
watchEffect(() => {
  useHead({
    title: `${t('menu.brand')} - ${t('home.title')}`,
  });
});

</script>
<template>
  <div class="padding-div">
    <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="20">
      <el-space :fill="true" style="width: 100%">
        <BrandCard/>
      </el-space>
    </el-col>
  </div>
</template>
<style scoped>
.padding-div {
  padding: 20px;
  overflow-x: auto; /* 允許橫向滑動 */
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch; /* 平滑滾動，適用於移動設備 */
}
.padding-div::-webkit-scrollbar {
  display: none; /* 隱藏滾動條 */
}
@media (max-width: 500px) {
  .padding-div {
    padding: 0px;
  }
}
</style>
