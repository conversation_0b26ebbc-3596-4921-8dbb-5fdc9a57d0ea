<template>
  <div class="trans-div">
    <el-button @click=changeTrans v-if="isTrans" type="primary">{{ $t('event.isTrans') }}</el-button>
    <el-button @click=changeTrans v-if="!isTrans" type="primary">{{ $t('brand.doTrans') }}</el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
const route = useRoute();
const language = ref(route.query.language || 'default');
const isTrans = ref(true);
const { t } = useI18n()
const emit = defineEmits(['update:isTrans']);
const props = defineProps({
  text: {//這邊傳進來有可能是brandID 有可能其實是brandWebIndex 要注意
    type: String,
    required: true,
  }
});
const buttonText = computed(() => {
  return `${t(`${props.text}`)}`;
});

function changeTrans() {//點擊按鈕
  isTrans.value = !isTrans.value;
}
onMounted(async () => {

});

watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
});

watch(() => isTrans.value, async () => {//切換時 回傳資料
  emit('update:isTrans', isTrans.value);
});

</script>

<style scoped></style>