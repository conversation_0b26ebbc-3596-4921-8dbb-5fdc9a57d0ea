import { AuthPayload } from "~~/models/user";
import { getInAppPayMemberInfo } from "~~/api/inAppPay";
import { globalStore } from "../stores/global";
import { supportLangs } from "~~/util/language";

let store = globalStore();

export default defineNuxtRouteMiddleware(async (to, from) => {
  if (to.redirectedFrom) return;
  const auth = to.query.auth;
  const token = to.query.token;
  let toLang = to.query.language as string;
  toLang = toLang && supportLangs.includes(toLang) ? toLang : store.getLanguage;
  to.query.language = toLang;
  store.setLanguage(toLang);
  store.setCurrentRoute(to.path.replace("/", ""));
  if (token) {//app端跳轉網頁 用token登入 轉至推廣頁面 但現在推廣頁面被砍掉了2024/06/04
    try {
      console.log(token);
      if (Array.isArray(token))
      throw console.error("token error");
      await store.signInWithToken(token);
      to.name = "promote";
      delete to.query.token;
      return navigateTo(to);
    } catch (error) {
      console.log(error);
      console.log("Unauthorized, redirect to /login");
      to.name = "login";
      delete to.query.token;
      return navigateTo(to);
    }
  }
  if (auth) {//auth登入 更早的app端跳轉網頁登入方法 是token的前一版 早就被砍掉了
    try {
      const payload = JSON.parse(window.atob(auth.toString())) as AuthPayload;
      const reqTime = new Date(payload.timestamp).getTime();
      const diff = (Date.now() - reqTime) / 1000; // seconds
      if (diff > 0 && diff < 60) {
        await store.updateCache(payload.userID, payload.accessKey);
        // 成功登入後把auth參數移除
        delete to.query.auth;
        return navigateTo(to);
      }
      to.name = "login";
      return navigateTo(to);
    } catch (error) {
      console.log("Unauthorized, redirect to /login");
      to.name = "login";
      return navigateTo(to);
    }
  }
  let user = store.getCurrentUser;
  if (!user) {
    console.log("Unauthorized, redirect to /login");
    to.name = "home";
    // return navigateTo(to);
  }
  store.setCurrentRoute(to.path.replace("/", ""));
  if (to.name == "index") {
    to.name = "home";
    // return navigateTo("/home");
  }
  return navigateTo(to);
});
