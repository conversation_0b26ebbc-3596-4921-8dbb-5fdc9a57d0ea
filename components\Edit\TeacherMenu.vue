<template>
  <el-dropdown @command="handleCommand" size="large">
    <span class="el-dropdown-link" @click.stop>
      <img src="../../public/_icons_/dropdown-icon.svg" alt="Custom Icon" style="width: 24px; height: 24px;" />
    </span>
    <template #dropdown>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item  class="dropdown-item" command="editTeacher">{{ $t('brand.edit') }}</el-dropdown-item>
        <el-dropdown-item v-if="canDelete" class="dropdown-item" command="deleteTeacher">{{ $t('brand.deleteTeacher') }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <EditDialog :type=type :teacherID=props.teacherID :teacherName=props.teacherName :visible=isDialogVisible :isInside=props.isInside @update:visible="isDialogVisible = $event"></EditDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BrandDetail } from "~~/models/brand";
const props = defineProps({
  teacherID: {
    type: String,
    default: '' // 
  },
  teacherName: {
    type: String,
    default: '' // 名字 刪除用
  },
  canDelete: {
    type: Boolean,
    default: false // 是否可以刪除
  },
  isInside: {
    type: Boolean,
    default: false // 是否在教師編輯頁面內編輯
  }
});

const isDialogVisible = ref(false);
const type = ref(1);
const handleCommand = (command: string) => {
  switch (command) {
    case 'editTeacher':
      editData();   
      break;
    case 'deleteTeacher':
      deleteTeacher();
      break;
  }
};

const editData = () => {
  console.log('編輯資料');
  type.value = 5;
  isDialogVisible.value = true;
};

const deleteTeacher = () => {
  console.log('刪除教師');
  type.value = 6;
  isDialogVisible.value = true;
};

</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
}
::v-deep .el-dropdown-menu__item {
  font-size: 16px; /* 調整字號 */
}
</style>