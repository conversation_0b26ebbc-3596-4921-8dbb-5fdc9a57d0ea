<template>
  <div>
    <div v-if="imageUrl && props.type === 1">
      <!--<el-avatar :style="{ minWidth: '100px' }" :size="100" :src="imageUrl"></el-avatar>-->
      <el-image :src="imageUrl" :preview-src-list="[imageUrl]" class="circle-image">
      </el-image>
    </div>
    <div v-if="imageUrl && props.type === 2">
      <div class="image-wrapper">
        <!--<img :src="imageUrl" class="image" alt="品牌封面" />-->
        <el-image :src="imageUrl" :preview-src-list="[imageUrl]" class="image">
        </el-image>
      </div>
    </div>
    <div v-if="!imageUrl && props.type === 1" class="upload-card1-1" @click="triggerUpload">
      <el-icon :size="40" class="upload-icon">
        <UploadFilled />
      </el-icon>
    </div>
    <div v-if="!imageUrl && props.type === 2" class="upload-card16-9" @click="triggerUpload">
      <el-icon :size="40" class="upload-icon">
        <UploadFilled />
      </el-icon>
    </div>
    <el-button v-if="imageUrl" @click="triggerUpload">{{ $t('edit.reUpload') }}</el-button>
    <input type="file" ref="fileInput" @change="handleFileChange" style="display: none" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import s3 from '../../awsConfig';
import { PutObjectCommand, PutObjectCommandInput } from '@aws-sdk/client-s3';
import { UploadFilled } from "@element-plus/icons-vue";
import { useI18n } from 'vue-i18n';
import { v4 as uuidv4 } from 'uuid';
import imageCompression from 'browser-image-compression';
const props = defineProps({
  type: {
    type: Number,
    required: true,
  },
  oldImageUrl: {//原先上傳過的照片
    type: String,
    required: true,
  },
  frontPath: {//圖片路徑 前半段
    type: String,
    required: true,
  },
  backPath: {//圖片路徑 後半段
    type: String,
    required: true,
  }
});
const config = useRuntimeConfig();
const envVersion = config.public.version || '';
const envPath = envVersion.includes('dev') ? 'develop' : 'production';
const emit = defineEmits(['update:imageUrl']);
const i18n = useI18n();
const imageUrl = ref('');
const fileInput = ref<HTMLInputElement | null>(null);

onMounted(() => {//如果原先有上傳過照片 自動帶入
  imageUrl.value = props.oldImageUrl || '';
});
watch(imageUrl, (newUrl) => {
  emit('update:imageUrl', newUrl);
});


const triggerUpload = () => {
  fileInput.value?.click();
};

const handleFileChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    let file = input.files[0];

    const isImage = /\.(png|jpg|jpeg|gif|bmp)$/i.test(file.name);
    if (!isImage) {
      ElMessage.error(i18n.t('edit.typeError'));
      return;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      ElMessage.warning(i18n.t('edit.tooBig'));
      try {
        file = await imageCompression(file, {
          maxSizeMB: 2, // 壓縮後的最大大小
          maxWidthOrHeight: 1920, // 壓縮後的最大寬高
          useWebWorker: true
        });
      } catch (err) {
        console.error("Error compressing file:", err);
        return;
      }
    }

    const uuid = uuidv4();//使用UUID 避免洩漏檔案名稱資料
    const extension = file.name.split('.').pop();
    const newFileName = `${uuid}.${extension}`;

    const params: PutObjectCommandInput = {
      Bucket: "itutbox",
      Key: `${props.frontPath}/${envPath}/${props.backPath}/${newFileName}`,
      Body: file,
      ContentType: file.type,
      ACL: "public-read"
    };

    try {
      const command = new PutObjectCommand(params);
      const response = await s3!.send(command);
      const imageUrlString = `https://${params.Bucket}.s3.ap-northeast-1.amazonaws.com/${params.Key}`;
      imageUrl.value = imageUrlString;
      ElMessage.success(i18n.t('edit.uploadSuccess'));
      console.log("Uploaded file successfully:", response);
    } catch (err) {
      ElMessage.error(i18n.t('edit.uploadError'));
      console.error("Error uploading file:", err);
    }
  }
};
</script>

<style scoped>
.image-wrapper {
  width: 100%;
  aspect-ratio: 16 / 9;
  /* 設置圖片的寬高比 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;

  margin-bottom: 10px;
}

.image {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}

.upload-card1-1 {
  width: 100px;
  height: 100px;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-card16-9 {
  width: 160px;
  height: 90px;
  border: 2px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-icon {
  color: #888888;
}

.circle-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

</style>