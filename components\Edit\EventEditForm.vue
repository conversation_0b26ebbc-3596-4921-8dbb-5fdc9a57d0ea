<template>
  <div v-if="!props.isPast" class="teacherEdit-form"><!--目前活動-->
    <el-form-item v-if="props.isAdd" class="responsive-form-item"><!--第一次新增時是primaryLanguage-->
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandLanguage') }}：
      </template>
      <div class="always-block">{{ $t('edit.primaryLanguageDescription') }}
        <div>
          <el-select v-model="eventEdit.primaryLanguage" placeholder="Select" style="width: 140px">
            <el-option v-for="lang in supportLangs" :key="lang" :label="$t(`Language.${lang}`)" :value="lang" />
          </el-select>
        </div>
      </div>
    </el-form-item>

    <el-form-item v-else><!--編輯時是editingLanguage-->
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandLanguage') }}：
      </template>
      <div class="always-block">{{ $t('edit.editingLanguageDescription') }}
        <div>
          <el-select v-model="eventEdit.editingLanguage" placeholder="Select" style="width: 140px">
            <el-option v-for="lang in supportLangs" :key="lang" :label="$t(`Language.${lang}`)" :value="lang" />
          </el-select>
        </div>
      </div>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.eventPhoto') }}：
      </template>
      <div class="always-block">{{ $t('edit.eventPhotoDescription') }}
        <EditUploadImage v-if="doneLoading" :oldImageUrl="eventEdit.photo" v-model:imageUrl="eventEdit.photo" :type="2"
          :frontPath="'store'" :backPath="'event'">
        </EditUploadImage>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('event.theme') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.name" :placeholder="$t('edit.inputEventName')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('event.introduction') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.description" type="textarea" :rows="4"
        :placeholder="$t('edit.inputEventDescription')"></el-input>
    </el-form-item>

    <el-form-item v-if="!props.onlyText" class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.eventPlace') }}
      </template>
      <div class="place-div">
        <div>
          <label>{{ $t('event.location') }}：</label>
          <EditGooglePlacesAutocomplete v-model="placeQuery" @place-selected="handlePlaceSelected" />
        </div>
        <div class="row">
          <label :style="{ whiteSpace: 'nowrap' }">{{ $t('edit.storeName') }}：</label>
          <el-input :disabled="!eventEdit.store.countryCode" v-model="eventEdit.store.storeName"></el-input>
        </div>
        <div class="row">
          <label :style="{ whiteSpace: 'nowrap' }">{{ $t('edit.storeAddress') }}：</label>
          <el-input :disabled="!eventEdit.store.countryCode" v-model="eventEdit.store.address"></el-input>
        </div>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.eventTeacher') }}：
      </template>
      <el-select :disabled="props.onlyText" v-model="eventEdit.teacherID" placeholder="Select" style="width: 140px">
        <el-option v-for="teacher in localizedTeachers" :key="teacher.teacherID" :label="teacher.name"
          :value="teacher.teacherID" />
      </el-select>
    </el-form-item>

    <el-form-item class="responsive-form-item" v-if="eventEdit.teacherID && eventEdit.teacherID != 'noteacher'">
      <template #label>
        <span style="color: red">*</span> {{ $t('event.teacherIntroduction') }}：
      </template>
      <el-input v-model="eventEdit.teacherDetail.description" type="textarea" :rows="4"
        :placeholder="$t('edit.inputTeacherDescription')"></el-input>
    </el-form-item>

    <el-form-item v-if="!props.onlyText">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.eventType') }}：
      </template>
      <el-select v-model="eventEdit.tagID" placeholder="Select" style="width: 140px">
        <el-option v-for="tag in tags" :key="tag?.tagID" :label="tag?.label" :value="tag?.tagID" />
      </el-select>
    </el-form-item>

    <el-form-item v-if="!props.onlyText">
      <template #label>
        <span style="color: red">*</span> {{ $t('event.eventTime') }}：
      </template>
      <el-date-picker v-model="eventEdit.eventTime.startTime" type="datetime" placeholder="Start date"
        :editable="false" />
      <el-date-picker v-model="eventEdit.eventTime.endTime" type="datetime" placeholder="End date" :editable="false" />
    </el-form-item>

    <el-form-item v-if="!props.onlyText" class="always-block">
      <template #label>
        <span style="color: red">*</span> {{ $t('event.generalAdmission') }}：
      </template>
      <div class="always-block">
        <div>{{ $t('edit.over500') }}</div>
        <div>
          <div class="price-gap">
            <label>{{ $t('event.nonMember') }}：</label>
            <el-input-number v-model="generalPrice" :controls="false" :min="0" :max="50000" />
            <label>，{{ $t('event.productPrice') }}：</label>
            <el-input-number v-model="oriPrice" :controls="false" :min="0" :max="50000" />
          </div>
          <div>
            <label>{{ $t('event.member') }}：</label>
            <el-input-number v-model="memberPrice" :controls="false" :disabled="true" />
            <label>，{{ $t('event.productPrice') }}：</label>
            <el-input-number v-model="memberOriPrice" :controls="false" :disabled="true" />
          </div>
        </div>
      </div>
    </el-form-item>

    <el-form-item v-if="!props.onlyText" class="always-block">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.earlyPrice') }}：
      </template>
      <div class="price-gap">
        <label>{{ $t('event.nonMember') }}：</label>
        <el-input-number v-model="earlyGenralPrice" :controls="false" :disabled="true" />
        <label>，{{ $t('event.productPrice') }}：</label>
        <el-input-number v-model="earlyOriPrice" :controls="false" :disabled="true" />
      </div>
      <div>
        <label>{{ $t('event.member') }}：</label>
        <el-input-number v-model="earlyMemberPrice" :controls="false" :disabled="true" />
        <label>，{{ $t('event.productPrice') }}：</label>
        <el-input-number v-model="earlyMemberOriPrice" :controls="false" :disabled="true" />
      </div>
    </el-form-item>

    <el-form-item v-if="!props.onlyText">
      <template #label>
        <span style="color: red">*</span> {{ $t('event.earlyBirdDeadline') }}：
      </template>
      <el-date-picker v-model="eventEdit.eventTime.earlyBirdEndTime" type="date"
        :placeholder="$t('event.earlyBirdDeadline')" />
    </el-form-item>

    <el-form-item v-if="!props.onlyText">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.totalQuantity') }}：
      </template>
      <el-input-number v-model="eventEdit.totalQuantity" :controls-position="'right'" :min="5" :max="200" />
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('event.brandIntroduction') }}：
      </template>
      <el-input v-model="eventEdit.brandDetail.description" type="textarea" :rows="2"
        :placeholder="$t('edit.inputBrandDescription')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('event.eventFeatures') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.feature" type="textarea" :rows="2"
        :placeholder="$t('edit.inputEventFeature')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('edit.meals') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.meals" type="textarea" :rows="2"
        :placeholder="$t('edit.inputMeals')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('event.locationIntroduction') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.storeDescription" type="textarea" :rows="2"
        :placeholder="$t('edit.locationIntroduction')"></el-input>
    </el-form-item>


  </div>
  <div v-else class="teacherEdit-form"><!--過往活動(不在我們平台上辦的)-->
    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandLanguage') }}：
      </template>
      <el-select v-model="eventEdit.editingLanguage" placeholder="Select" style="width: 140px">
        <el-option v-for="lang in supportLangs" :key="lang" :label="$t(`Language.${lang}`)" :value="lang" />
      </el-select>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.eventPhoto') }}：
      </template>
      <EditUploadImage v-if="doneLoading" :oldImageUrl="eventEdit.photo" v-model:imageUrl="eventEdit.photo" :type="2"
        :frontPath="'brand'" :backPath="'pastEvent/photo'">
      </EditUploadImage>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('event.theme') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.name" :placeholder="$t('edit.inputEventName')"></el-input>
    </el-form-item>

    <el-form-item v-if="!props.onlyText" class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.eventPlace') }}
      </template>
      <div class="place-div">
        <div class="row">
          <label :style="{ whiteSpace: 'nowrap' }">{{ $t('edit.storeName') }}：</label>
          <el-input v-model="eventEdit.store.storeName"></el-input>
        </div>
        <div class="row">
          <label :style="{ whiteSpace: 'nowrap' }">{{ $t('edit.storeAddress') }}：</label>
          <el-input v-model="eventEdit.store.address"></el-input>
        </div>
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('event.eventTime') }}：
      </template>
      <el-date-picker v-model="eventEdit.eventTime.startTime" type="datetime" placeholder="Start date" />
      <el-date-picker v-model="eventEdit.eventTime.endTime" type="datetime" placeholder="End date" />
    </el-form-item>

    <el-form-item>
      <template #label>
        {{ $t('edit.eventTeacher') }}：
      </template>
      <el-select :disabled="props.onlyText" v-model="eventEdit.teacherID" placeholder="Select" style="width: 140px">
        <el-option v-for="teacher in localizedTeachers" :key="teacher.teacherID" :label="teacher.name"
          :value="teacher.teacherID" />
      </el-select>
    </el-form-item>

    <el-form-item class="responsive-form-item" v-if="eventEdit.teacherID && eventEdit.teacherID != 'noteacher'">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.teacherDescription') }}：
      </template>
      <el-input v-model="eventEdit.teacherDetail.description" type="textarea" :rows="4"
        :placeholder="$t('edit.inputTeacherDescription')"></el-input>
    </el-form-item>

    <el-form-item class="always-block">
      <template #label>
        {{ $t('event.generalAdmission') }}：
      </template>
      <div class="price-gap">
        <label>{{ $t('event.nonMember') }}：</label>
        <el-input-number v-model="generalPrice" :controls="false" :min="0" :max="50000" />
        <label>，{{ $t('event.productPrice') }}：</label>
        <el-input-number v-model="oriPrice" :controls="false" :min="0" :max="50000" />
      </div>
      <div>
        <label>{{ $t('event.member') }}：</label>
        <el-input-number v-model="memberPrice" :controls="false" :disabled="true" />
        <label>，{{ $t('event.productPrice') }}：</label>
        <el-input-number v-model="memberOriPrice" :controls="false" :disabled="true" />
      </div>
    </el-form-item>

    <el-form-item class="always-block">
      <template #label>
        {{ $t('edit.earlyPrice') }}：
      </template>
      <div class="price-gap">
        <label>{{ $t('event.nonMember') }}：</label>
        <el-input-number v-model="earlyGenralPrice" :controls="false" :disabled="true" />
        <label>，{{ $t('event.productPrice') }}：</label>
        <el-input-number v-model="earlyOriPrice" :controls="false" :disabled="true" />
      </div>
      <div>
        <label>{{ $t('event.member') }}：</label>
        <el-input-number v-model="earlyMemberPrice" :controls="false" :disabled="true" />
        <label>，{{ $t('event.productPrice') }}：</label>
        <el-input-number v-model="earlyMemberOriPrice" :controls="false" :disabled="true" />
      </div>
    </el-form-item>

    <el-form-item>
      <template #label>
        {{ $t('event.earlyBirdDeadline') }}：
      </template>
      <el-date-picker v-model="eventEdit.eventTime.earlyBirdEndTime" type="date"
        :placeholder="$t('event.earlyBirdDeadline')" />
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('event.introduction') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.description" type="textarea" :rows="4"
        :placeholder="$t('edit.inputEventDescription')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('edit.brandDescription') }}：
      </template>
      <el-input v-model="eventEdit.brandDetail.description" type="textarea" :rows="2"
        :placeholder="$t('edit.inputBrandDescription')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('event.eventFeatures') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.feature" type="textarea" :rows="2"
        :placeholder="$t('edit.inputEventFeature')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('edit.meals') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.meals" type="textarea" :rows="2"
        :placeholder="$t('edit.inputMeals')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('event.locationIntroduction') }}：
      </template>
      <el-input v-model="eventEdit.eventDetail.storeDescription" type="textarea" :rows="2"
        :placeholder="$t('edit.locationIntroduction')"></el-input>
    </el-form-item>


  </div>
  <el-button v-if="!props.isPast" type="primary" size="large" @click="save">{{ $t('edit.save') }}</el-button>
  <el-button v-else type="primary" size="large" @click="pastSave">{{ $t('edit.save') }}</el-button>
</template>

<script setup lang="ts">
//我很抱歉這個組件這麼大一包 因為有五種狀況都用到這個組件 拆開來寫會更混亂
import { ref } from 'vue';
import { supportLangs } from "~~/util/language";
import { TeacherDetail, TeacherCard } from "../../models/teacher"
import { EventDetail, EventEdit, Store, EventTags } from "../../models/event"
import { BrandDetail } from "../../models/brand"
import { getBrandDetail } from "../../api/brand"
import { getTeacherDetail, getTeacherCard } from "../../api/teacher"
import { getEventDetail, getEventTags, addEvent, editEvent, editTextEvent, formatEditDate, addPastEvent, editPastEvent } from "../../api/event"
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import cloneDeep from 'lodash/cloneDeep';//相當於原生structuredClone 但後者不確定環境是否支援
const props = defineProps({
  eventID: {
    type: String,
    default: '' // 編輯活動時 活動ID
  },
  brandID: {
    type: String,
    default: '' // 所屬品牌ID
  },
  isAdd: {
    type: Boolean,
    default: false // 新增活動
  },
  isPast: {
    type: Boolean,
    default: false // 新增編輯過往活動
  },
  onlyText: {
    type: Boolean,
    default: false // 只有編輯文字功能
  }
});
const { t } = useI18n();
const i18n = useI18n();
//#region 這部分參數與表單綁定 或是做為選項
const placeQuery = ref('');
const teachers = ref<TeacherCard[]>([//call teacher/card 但要先手動加一個 沒有教師
  {
    teacherID: 'noteacher',
    photo: '',
    name: 'noteacher',
    rating: 0,
    teacherWebIndex: '',
    nationality: '',
    primaryLanguage: '',
    hasEvent: false
  }]);
const tags = ref<EventTags[]>();
const generalPrice = ref(0);
const oriPrice = ref(0);
const doneLoading = ref(false);//初始化完成 避免舊圖片顯示不出來
const initialEventEdit = {//初始值
  eventWebIndex: "",
  store: {
    storeName: "",
    address: "",
    location: {
      longitude: 0,
      latitude: 0
    },
    countryCode: ""
  },
  storeID: "",
  brandID: "",
  teacherID: "",
  tagID: "",
  primaryLanguage: "",
  editingLanguage: "",
  eventDetail: {
    name: "",
    description: "",
    feature: "",
    storeDescription: "",
    meals: ""
  },
  brandDetail: {
    name: "",
    description: ""
  },
  teacherDetail: {
    name: "",
    description: ""
  },
  photo: "",
  totalQuantity: 0,
  eventTime: {
    earlyBirdEndTime: "",
    startTime: "",
    endTime: ""
  },
  ticketType: [
    {
      "ticketTypeID": 0,
      "ticketTypeName": "一般票",
      "price": 0,
      "originalPrice": 0
    },
    {
      "ticketTypeID": 1,
      "ticketTypeName": "會員票",
      "price": 0,
      "originalPrice": 0
    },
    {
      "ticketTypeID": 2,
      "ticketTypeName": "早鳥一般票",
      "price": 0,
      "originalPrice": 0
    },
    {
      "ticketTypeID": 3,
      "ticketTypeName": "早鳥會員票",
      "price": 0,
      "originalPrice": 0
    }
  ],
};
const teacherDetail = ref<TeacherDetail>();
const eventDetail = ref<EventDetail>();//編輯時抓的預設參數
const eventEdit = ref<EventEdit>(cloneDeep(initialEventEdit));//主要參數
//#endregion
const brand = ref<BrandDetail>();

onMounted(async () => {//初始化 如果eventDetail有值(編輯時) 帶入資料
  if (!props.isAdd)//如果不是新增活動，就需要先抓一次資料
  {
    eventDetail.value = await getEventDetail({ eventID: props.eventID, isPlatformEvent: !props.isPast });
    teachers.value.push(...await getTeacherCard({ brandID: eventDetail.value.brandID }));//抓取老師列表 做為選項
    eventEdit.value.editingLanguage = eventDetail.value.oriLanguage
    eventEdit.value.store.storeName = eventDetail.value.storeName;
    eventEdit.value.store.address = eventDetail.value.address;
    eventEdit.value.storeID = eventDetail.value.storeID;//如果完全沒改store store給null 但給storeID
    eventEdit.value.brandID = eventDetail.value.brandID;
    eventEdit.value.teacherID = eventDetail.value.teacherID || 'noteacher';
    eventEdit.value.tagID = eventDetail.value.tagID;
    eventEdit.value.eventWebIndex = eventDetail.value.eventWebIndex;
    eventEdit.value.eventDetail = cloneDeep(eventDetail.value.oriEventDetail);
    eventEdit.value.brandDetail = cloneDeep(eventDetail.value.oriBrandDetail);
    eventEdit.value.teacherDetail = cloneDeep(eventDetail.value.oriTeacherDetail);
    eventEdit.value.photo = eventDetail.value.photo;
    eventEdit.value.totalQuantity = eventDetail.value.totalQuantity;
    eventEdit.value.eventTime.earlyBirdEndTime = eventDetail.value.localEarlyBirdEndTime;
    eventEdit.value.eventTime.startTime = eventDetail.value.localStartTime;
    eventEdit.value.eventTime.endTime = eventDetail.value.localEndTime;
    eventEdit.value.ticketType = cloneDeep(eventDetail.value.ticketType);
    generalPrice.value = (eventDetail.value.ticketType.find(ticket => ticket.ticketTypeID === 0) || { price: 0 }).price;
    oriPrice.value = (eventDetail.value.ticketType.find(ticket => ticket.ticketTypeID === 0) || { originalPrice: 0 }).originalPrice;
  }
  else {
    brand.value = await getBrandDetail({ brandID: props.brandID });//新增活動時抓品牌資料
    eventEdit.value.brandDetail.description = brand.value.oriDescription;
    eventEdit.value.brandDetail.name = brand.value.name;
    teachers.value.push(...await getTeacherCard({ brandID: props.brandID }));//用傳入的品牌ID抓取老師列表
  }
  doneLoading.value = true;

  tags.value = await getEventTags();//抓取標籤資料
  console.log(teachers.value)
});

watch(() => eventEdit.value.primaryLanguage, async (newLang) => {//新增活動時 切換編輯語言時會切換資料
  console.log(newLang)
  console.log(initialEventEdit.eventDetail)
  eventEdit.value.eventDetail = cloneDeep(initialEventEdit.eventDetail);
  eventEdit.value.teacherDetail = cloneDeep(initialEventEdit.teacherDetail);
  if (newLang != brand.value?.oriLanguage)
    eventEdit.value.brandDetail.description = initialEventEdit.brandDetail.description;
  else
    eventEdit.value.brandDetail.description = brand.value.oriDescription
});
watch(() => eventEdit.value.editingLanguage, async (newLang) => {//切換編輯語言時會重抓資料
  if (eventDetail.value) {//確定資料已抓到
    if (newLang != eventDetail.value.oriLanguage)//如果跟之前的不一樣 清空名字與描述 避免語言錯誤
    {
      eventEdit.value.eventDetail = cloneDeep(initialEventEdit.eventDetail);
      eventEdit.value.teacherDetail.description = initialEventEdit.teacherDetail.description;
      eventEdit.value.brandDetail.description = initialEventEdit.brandDetail.description;
    } else {//如果一樣 就再帶入上次資料
      eventEdit.value.eventDetail = cloneDeep(eventDetail.value.oriEventDetail);
      eventEdit.value.teacherDetail.description = eventDetail.value.oriTeacherDetail.description;
      eventEdit.value.brandDetail.description = eventDetail.value.oriBrandDetail.description;
    }
  }
});

watch(() => eventEdit.value.eventTime.startTime, async (newTime) => {//自動帶入活動結束時間與早鳥期限
  if (props.isAdd) {
    eventEdit.value.eventTime.earlyBirdEndTime = calculateEarlyBirdEndTime(newTime);//自動帶入十四天前
    eventEdit.value.eventTime.endTime = newTime;//自動帶入同一天
    console.log(newTime)
    console.log(eventEdit.value.eventTime.earlyBirdEndTime)
  }
});
function calculateEarlyBirdEndTime(startTime: string): string {//早鳥期限計算
  const currentTime = new Date();
  const earlyBirdDate = new Date(startTime);
  earlyBirdDate.setUTCDate(earlyBirdDate.getUTCDate() - 14);
  earlyBirdDate.setUTCHours(0, 0, 0, 0);

  if (earlyBirdDate < currentTime && (!props.isPast)) {
    return currentTime.toISOString().substring(0, 16);
  } else {
    return earlyBirdDate.toISOString().substring(0, 16);
  }
}

const handlePlaceSelected = (place: Store) => {// 帶入從查詢地點組件取回的資料
  console.log(place)
  eventEdit.value.store.storeName = place.storeName;
  eventEdit.value.store.address = place.address;
  eventEdit.value.store.location.latitude = place.location.latitude;
  eventEdit.value.store.location.longitude = place.location.longitude;
  eventEdit.value.store.countryCode = place.countryCode;
};

const localizedTeachers = computed(() => {//為了多語言化選項：無須教師
  return teachers.value.map(teacher => {
    if (teacher.teacherID === 'noteacher') {
      return {
        ...teacher,
        name: t('edit.noTeacher')
      };
    }
    return teacher;
  });
});

watch(() => eventEdit.value.teacherID, async (newTeacher) => {//切換活動教師時，抓教師介紹
  console.log(newTeacher)
  if (newTeacher != 'noteacher' && newTeacher != '')//如果有選擇教師，抓teacher/Detail
  {
    teacherDetail.value = await getTeacherDetail({ teacherID: newTeacher })
    eventEdit.value.teacherDetail.description = teacherDetail.value.oriDescription;
    eventEdit.value.teacherDetail.name = teacherDetail.value.teacherName;
  } else {//如果選無教師，清空
    eventEdit.value.teacherDetail.description = '';
    eventEdit.value.teacherDetail.name = '';
  }
});

const memberPrice = computed(() => Math.max(generalPrice.value - 200, 0));//全是票價計算
const memberOriPrice = computed(() => Math.max(oriPrice.value - 200, 0));
const earlyGenralPrice = computed(() => Math.max(generalPrice.value - 200, 0));
const earlyOriPrice = computed(() => Math.max(oriPrice.value - 200, 0));
const earlyMemberPrice = computed(() => Math.max(generalPrice.value - 400, 0));
const earlyMemberOriPrice = computed(() => Math.max(oriPrice.value - 400, 0));

const save = async () => {//儲存 會檢查然後上傳(目前活動版)
  //#region 檢查欄位是否都正確
  if (props.isAdd) {//新增活動
    if (!eventEdit.value.primaryLanguage || !eventEdit.value.photo || !eventEdit.value.eventDetail.name || !eventEdit.value.eventDetail.description ||
      !eventEdit.value.store.storeName || !eventEdit.value.store.address || !eventEdit.value.store.location.latitude || !eventEdit.value.store.location.longitude || !eventEdit.value.store.countryCode ||
      !eventEdit.value.brandDetail.description || !eventEdit.value.teacherID || !eventEdit.value.tagID ||
      !eventEdit.value.eventTime.startTime || !eventEdit.value.eventTime.endTime || !generalPrice.value || !eventEdit.value.eventTime.earlyBirdEndTime ||
      !eventEdit.value.totalQuantity) {
      ElMessage.error(i18n.t('edit.cannotBeEmpty'));
      return;
    }
  }
  else {//編輯活動
    if (props.onlyText) {//僅文字
      if (!eventEdit.value.editingLanguage || !eventEdit.value.photo || !eventEdit.value.eventDetail.name || !eventEdit.value.eventDetail.description ||
        !eventEdit.value.brandDetail.description) {
        ElMessage.error(i18n.t('edit.cannotBeEmpty'));
        return;
      }
    }
    else {//全部
      if (!eventEdit.value.editingLanguage || !eventEdit.value.photo || !eventEdit.value.eventDetail.name || !eventEdit.value.eventDetail.description ||
        !eventEdit.value.store.storeName || !eventEdit.value.store.address ||
        !eventEdit.value.brandDetail.description || !eventEdit.value.teacherID || !eventEdit.value.tagID ||
        !eventEdit.value.eventTime.startTime || !eventEdit.value.eventTime.endTime || !generalPrice.value || !eventEdit.value.eventTime.earlyBirdEndTime ||
        !eventEdit.value.totalQuantity) {
        ElMessage.error(i18n.t('edit.cannotBeEmpty'));
        return;
      }
    }
  }

  //#endregion
  //#region 檢查日期、票價 再賦值
  if (oriPrice.value < 600 || generalPrice.value < 600) {//檢查票價
    ElMessage.error(i18n.t('edit.over500'));
    return;
  }
  const startTime = new Date(eventEdit.value.eventTime.startTime);
  const endTime = new Date(eventEdit.value.eventTime.endTime);
  const earlyBirdEndTime = new Date(eventEdit.value.eventTime.earlyBirdEndTime);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (
    endTime < startTime || //開始時間不能晚於結束時間
    startTime < earlyBirdEndTime ||//早鳥時間不能早於開始時間
    (earlyBirdEndTime < today && props.isAdd)//新活動早鳥時間不能早於今天
  )//檢查時間
  {
    ElMessage.error(i18n.t('edit.timeError'));
    return;
  }
  eventEdit.value.brandID = props.brandID;
  eventEdit.value.eventTime = {
    earlyBirdEndTime: formatEditDate(new Date(eventEdit.value.eventTime.earlyBirdEndTime)),
    startTime: formatEditDate(new Date(eventEdit.value.eventTime.startTime)),
    endTime: formatEditDate(new Date(eventEdit.value.eventTime.endTime))
  };
  eventEdit.value.ticketType[0].price = generalPrice.value;
  eventEdit.value.ticketType[0].originalPrice = oriPrice.value;
  eventEdit.value.ticketType[1].price = memberPrice.value;
  eventEdit.value.ticketType[1].originalPrice = memberOriPrice.value;
  eventEdit.value.ticketType[2].price = earlyGenralPrice.value;
  eventEdit.value.ticketType[2].originalPrice = earlyOriPrice.value;
  eventEdit.value.ticketType[3].price = earlyMemberPrice.value;
  eventEdit.value.ticketType[3].originalPrice = earlyMemberOriPrice.value;
  if (eventEdit.value.teacherID == "noteacher")
    eventEdit.value.teacherID = '';
  console.log(eventEdit.value);
  //#endregion
  //#region Call API
  let status = 0;
  if (props.isAdd) {
    status = await addEvent(eventEdit.value);//新增活動
  } else {
    if (props.onlyText) {//僅文字
      status = await editTextEvent(props.eventID, eventEdit.value);
    }
    else {//全部
      if (eventEdit.value.store.countryCode) //有修改過店家
        status = await editEvent(props.eventID, eventEdit.value, true);
      else
        status = await editEvent(props.eventID, eventEdit.value, false);
    }
  }
  //#endregion
  window.location.reload();//這裡要刷新一次頁面重callAPI 避免奇妙bug*/
}

const pastSave = async () => {//儲存 會檢查然後上傳(過往活動版)
  //#region 檢查欄位是否都正確
  if (!eventEdit.value.editingLanguage || !eventEdit.value.photo || !eventEdit.value.eventDetail.name ||
    !eventEdit.value.store.storeName || !eventEdit.value.store.address ||
    !eventEdit.value.eventTime.startTime || !eventEdit.value.eventTime.endTime) {
    ElMessage.error(i18n.t('edit.cannotBeEmpty'));
    return;
  }
  //#endregion
  //#region 檢查日期、票價 再賦值
  if (generalPrice.value && (oriPrice.value < 600 || generalPrice.value < 600)) {//檢查票價
    ElMessage.error(i18n.t('edit.over500'));
    return;
  }
  const startTime = new Date(eventEdit.value.eventTime.startTime);
  const endTime = new Date(eventEdit.value.eventTime.endTime);
  const earlyBirdEndTime = new Date(eventEdit.value.eventTime.earlyBirdEndTime);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  if (
    endTime < startTime || //開始時間不能晚於結束時間
    startTime < earlyBirdEndTime ||//早鳥時間不能早於開始時間
    (endTime > today)//過往活動 結束時間要早於今天
  )//檢查時間
  {
    ElMessage.error(i18n.t('edit.timeError'));
    return;
  }
  eventEdit.value.brandID = props.brandID;
  eventEdit.value.eventTime = {
    earlyBirdEndTime: formatEditDate(new Date(eventEdit.value.eventTime.earlyBirdEndTime)),
    startTime: formatEditDate(new Date(eventEdit.value.eventTime.startTime)),
    endTime: formatEditDate(new Date(eventEdit.value.eventTime.endTime))
  };
  if (generalPrice.value) {
    eventEdit.value.ticketType[0].price = generalPrice.value;
    eventEdit.value.ticketType[0].originalPrice = oriPrice.value;
    eventEdit.value.ticketType[1].price = memberPrice.value;
    eventEdit.value.ticketType[1].originalPrice = memberOriPrice.value;
    eventEdit.value.ticketType[2].price = earlyGenralPrice.value;
    eventEdit.value.ticketType[2].originalPrice = earlyOriPrice.value;
    eventEdit.value.ticketType[3].price = earlyMemberPrice.value;
    eventEdit.value.ticketType[3].originalPrice = earlyMemberOriPrice.value;
  } else {
    eventEdit.value.ticketType = [];//沒填價格就清空
  }
  if (eventEdit.value.teacherID == "noteacher")
    eventEdit.value.teacherID = '';
  console.log(eventEdit.value);
  //#endregion
  //#region Call API
  let status = 0;
  if (props.isAdd) {
    status = await addPastEvent(eventEdit.value);//新增過往活動
  } else {
    console.log(eventEdit.value)
    status = await editPastEvent(props.eventID, eventEdit.value);//編輯過往活動
  }
  //#endregion
  window.location.reload();//這裡要刷新一次頁面重callAPI 避免奇妙bug*/
}
</script>

<style scoped>
.teacherEdit-form {
  padding-bottom: 10px;
}

.teacherEdit-form :deep(.el-form-item__label) {
  padding-right: 0;
}

.upload16-9 {
  display: flex;
  flex-wrap: wrap;
}

.upload16-9 .el-upload {
  width: 178px;
  /* 16:9 aspect ratio */
  height: 100px;
  /* 16:9 aspect ratio */
}

.el-upload-list__item-thumbnail {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.el-icon-plus {
  font-size: 28px;
  color: #8c939d;
}

.price-gap {
  margin-bottom: 10px;
}

.place-div {
  width: 100%;
  margin-left: 14px;
  gap: 10px;
}

.row {
  display: flex;
  margin-top: 10px;
}

.always-block {
  display: block;
  /* 改變為 block 讓其換行 */
}

@media (max-width: 600px) {
  .responsive-form-item {
    display: block;
    /* 改變為 block 讓其換行 */
  }

  .place-div {
    margin-left: 0px;
  }
}
</style>