<script setup lang="ts">
import { globalStore } from "../../stores/global";
const store = globalStore();
definePageMeta({
  middleware: ["unauth"],
});
function handleToAccountClick() {
  let user = store.getCurrentUser;
  if (user) {//已登入
    navigateTo('/account');
  }
  else {//未登入
    navigateTo('/login');
  }
};
const props = defineProps({
  language: {//圖片語言
    type: String,
    required: true,
  },
});
</script>

<template>
  <div class="appIntro-page">
    <!-- APP 下載卡片 -->
    <el-card class="appDownload-card">
      <div class="appDownload-content">
        <div @click="handleToAccountClick" class="image-content">
          <img :src="`https://itut-website.s3.ap-northeast-1.amazonaws.com/public/AD/webADs_${props.language}.png`" alt="App Logo" class="app-logo right" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.appIntro-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0px 20px 0px 20px;
}

.appDownload-card {
  padding: 20px;
  background: linear-gradient(to bottom, #f8fcff, #ebf3fe);
  /* 背景漸層 */
}

.appDownload-content {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
}

.text-content {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  color: #27465e;
  /* 文字內文顏色 */
  font-weight: bold;
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.5;
  /* 行距 */
  font-size: 24px;
  /* 字體大小 */
  margin-left: 65px;
}

.image-content {
  max-width: 700px;
  max-height: 700px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  /* 確保裡面的物件不要超出這個 div */
  transition: transform 0.3s ease;
  /* 平滑的放大過渡效果 */
  cursor: pointer;
  /* 確保鼠標移到圖片上時顯示為點擊狀態 */
}

.image-content:hover {
  transform: scale(1.02);
  /* 當鼠標懸停時放大圖片 */
}

.app-logo {
  width: calc(100% - 5px);
  /* 每張圖片占容器寬度的一半 */
  height: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  /* 保持比例並確保圖片在容器內完整顯示 */
}

.app-logo.left {
  margin-right: 10px;
  /* 左邊圖片的右邊間距 */
}

@media (max-width: 500px) {
  .image-content {
    justify-content: center;
    text-align: center;
  }

  .appIntro-page {
    padding: 0px;
  }

  .appDownload-card {
    padding: 0px;
  }
}

@media (max-width: 800px) {
  .image-content {
    justify-content: center;
    text-align: center;
  }

  .text-content {
    justify-content: center;
    text-align: center;
    margin-left: 0px;
  }
}

@media (min-width: 1500px) {
  .appIntro-page {
    padding-right: 100px;
  }
}

@media (min-width: 1600px) {
  .appIntro-page {
    padding-right: 200px;
  }

  .text-content {
    font-size: 28px;
  }
}

@media (min-width: 1700px) {
  .appIntro-page {
    padding-right: 300px;
  }
}
</style>