<script lang="ts" setup>
import { MoreFilled } from "@element-plus/icons-vue";
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
const emit = defineEmits<{
  (e: "onDrawerExpended"): void;
}>();
const language = ref(route.query.language || 'default');
watch(
  () => route.query.language,
  (newLanguage) => {
    language.value = newLanguage || 'default';
  }
);
function handleLogoClick() {//回首頁
  const targetUrl = `/event?language=${language}`;
  router.push(targetUrl);
}


</script>

<template>
  <div class="header-menu desktop_view"><!-- 電腦版 -->
    <el-row class="row-bg" justify="space-between">
      <el-col :xs="22" :sm="14" :md="12" :lg="8" :xl="6">
        <div class="content-container" @click=handleLogoClick()>
          <a v-if="language !== 'default'"> <img :src="`/banner/banner_${language}.svg`" class="banner" /> </a>
          <!-- 在登入頁面 取得的語言一定會是default 所以做一個預設版的 -->
          <a v-if="language == 'default'" > <img src="/itut_logo.png" class="default-banner" /> </a>
          <a v-if="language == 'zh'"> <img src="/banner/AI_slogan.png" class="banner" /> </a>
        </div>
      </el-col>
      <el-col :xs="2" :sm="10" :md="12" :lg="16" :xl="18" class="grid-content">
        <el-row class="row-bg" justify="end">
          <el-space alignment="start" :size="20">
            <!-- TODO: 發現多個dropdown在同一個頁面顯示item會有問題，先做`語言`選擇 -->
            <UtilLangDropdown />
          </el-space>
        </el-row>
      </el-col>
    </el-row>
  </div>
  <div class="header-menu mobile_view"><!-- 手機版 -->
    <el-row class="row-bg" align="middle">
      <!-- 左側的 el-button -->
      <el-col :span="5" class="grid-content">
        <el-button class="mobile_view" type="text" plain size="large" @click="$emit('onDrawerExpended')">
          <img src="/_icons_/rowx3.svg" alt="Custom Icon" style="width: 32px; height: 32px;" />
        </el-button>
      </el-col>
      <!-- 中間的 Logo -->
      <el-col :span="14" class="logo-container">
        <div class="content-container" @click="handleLogoClick()">
          <a v-if="language !== 'default'" > <img :src="`/banner/banner_${language}.svg`" class="banner" /> </a>
          <a v-if="language == 'default'" > <img src="/itut_logo.png" class="default-banner" /> </a>
        </div>
      </el-col>
      <!-- 右側空白區域 -->
      <el-col :span="5"></el-col>
    </el-row>
  </div>
</template>

<style scoped>
.header-menu {
  border-bottom: solid 1px #e6e6e6;
  height: 59px;
  align-content: center;
}

.grid-content {
  min-height: 55px;
  width: 200px;
}

.itutLogo {
  min-height: 50px;
  width: 240px;
}

.owlLogo {
  min-height: 50px;
}
.banner {
  height: 60px;
}

.default-banner{
  height: 40px;
}

.wordLogo {
  height: 30px;
  margin-left: 4px;
  margin-bottom: 4px;
}

.el-col {
  border-radius: 4px;
}

.grid-content {
  border-radius: 4px;
  min-height: 36px;
}

.row-bg {
  padding: 0;
  height: 100%;
  align-content: center;
}

.content-container {
  display: flex;
  align-items: center;
  /* 垂直居中對齊 */
  cursor: pointer;
  /*出現手指*/
}

.slogan-container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  /* 讓文字容器佔據剩餘空間 */
  min-width: 0;
  max-width: 188px;
}

.text-content {
  display: flex;
  flex-direction: column;
  margin-left: 3px;
  flex-grow: 1;
  /* 讓文字容器佔據剩餘空間 */
  min-width: 0;
  /* 防止子元素過度擴展 */
}

.text-line {
  font-family: 'Arial', sans-serif;
  /* 設置字型 */
  font-size: 20px;
  /* 字型大小 */
  line-height: 1;
  /* 行距 */
  margin-left: 0px;
  /* 上下左右邊距 */
  color: #666666;
  /* 字體顏色 */
  font-weight: bold;
  /* 粗體 */
  letter-spacing: 1px;
  /* 字距 */
}

.text-slogan {
  font-family: 'Arial', sans-serif;
  /* 設置字型 */
  font-size: 16px;
  /* 字型大小 */
  line-height: 1;
  /* 行距 */
  margin-left: 12px;
  /* 上下左右邊距 */
  color: #666666;
  /* 字體顏色 */
  font-weight: bold;
  /* 粗體 */
  letter-spacing: 1px;
  /* 字距 */
}

.el-space__item>.mobile_view {
  margin-right: 0;
}

.logo-container {
  display: flex;
  justify-content: center;
  /* 確保 Logo 在中間 */
  align-items: center;
}

@media (max-width: 500px) {
  .owlLogo {
    min-height: 40px;
  }

  .wordLogo {
    height: 25px;
    margin-left: 4px;
    margin-bottom: 4px;
  }

  .text-slogan {
    font-size: 14px;
  }
}

@media (max-width: 1000px) {
  .text-slogan {
    margin-left: 6px;
  }
}
</style>
