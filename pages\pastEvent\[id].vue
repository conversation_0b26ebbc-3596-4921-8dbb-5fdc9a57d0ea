<script lang="ts" setup>
import { onMounted } from "vue";
import { useRoute } from 'vue-router';
definePageMeta({
  middleware: ["check-browser","unauth"],
});
const route = useRoute();
const eventID = route.params.id;
onMounted(async () => {//進入畫面時
  console.log(eventID);//現在路由的ID部分 有可能是eventID或是eventWebIndex
});

</script>
<template>
  <el-col :xs="24" :sm="24" :md="24" :lg="20" :xl="16">
    <el-space :fill="true" style="width: 100%">
      <EventDetailPage  :eventID="eventID" :isPlatformEvent=false />
    </el-space>
  </el-col>
</template>