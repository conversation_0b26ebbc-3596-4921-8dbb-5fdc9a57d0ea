<template>
  <div class="content-wrapper">
    <div><span class="bold">{{ $t("event.theme") }}：</span>{{ eventCard.eventName }}</div>
    <div v-if="eventCard.teacherName"><span class="bold">{{ $t("event.teacher") }}：</span>{{ eventCard.teacherName }}
    </div>
    <div><span class="bold">{{ $t("event.brand") }}：</span>{{ eventCard.brandName }}</div>
    <div v-if="!eventCard.teacherName"><span class="bold">{{ $t("event.location") }}：</span>{{ eventCard.storeName }}
    </div>
    <div><span class="bold">{{ $t("event.address") }}：</span>{{ eventCard.address }}</div>
    <div><span class="bold">{{ $t("event.eventTime") }}：</span>{{ formatEventTime(eventCard.localStartTime,
      eventCard.localEndTime, eventCard.week) }}</div>
    <div><span class="bold">{{ $t("event.totalQuantity") }}：</span>{{ eventCard.totalQuantity }}</div>
  </div>
  <div>
    <div v-if="participants?.attendedList.length" class="title"><span class="bold">{{ $t("event.attended") }}({{
      participants?.attendedList.length }})</span></div>
    <div v-if="participants?.attendedList.length">
      <el-card shadow="never" class="card">
        <div class="text" v-for="(people) in participants?.attendedList">
          <div><span class="bold">{{ $t("event.name") }}：</span>{{ people.userName }}</div>
          <div><span class="bold">Email：</span>{{ people.email }}</div>
          <div><span class="bold">{{ $t("event.ticketType") }}：</span>{{ t(`event.ticketType${people.ticketTypeID}`) }}</div>
          <hr class="divider" > 
        </div>
      </el-card>
    </div>
    <div v-if="participants?.notAttendedList.length" class="title"><span class="bold">{{ $t("event.notAttended") }}({{
      participants?.notAttendedList.length }})</span></div>
    <div v-if="participants?.notAttendedList.length">
      <el-card shadow="never" class="card">
        <div class="text" v-for="(people) in participants?.notAttendedList">
          <div><span class="bold">{{ $t("event.name") }}：</span>{{ people.userName }}</div>
          <div><span class="bold">Email：</span>{{ people.email }}</div>
          <div><span class="bold">{{ $t("event.ticketType") }}：</span>{{ t(`event.ticketType${people.ticketTypeID}`) }}</div>
          <hr class="divider" > 
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { eventParticipants, formatEventTime } from "../../api/event";
import { getGuestKey } from "../../api/user";
import { EventCard, Participants } from "~~/models/event";
import { User } from "~~/models/user";
import { globalStore } from "../../stores/global";
import { useI18n } from "vue-i18n";
import { useRouter } from 'vue-router';
const router = useRouter();
const { t } = useI18n()
const route = useRoute();
const store = globalStore();
const loading = ref(true);
const isTrans = ref(true);
let user: User;
const language = ref(route.query.language || 'default');
const participants = ref<Participants>();
const props = defineProps<{
  eventCard: EventCard;//直接拿整個brandDetail
}>();
onMounted(async () => {//進入畫面時
  participants.value = await eventParticipants(props.eventCard.eventID);
  console.log(participants.value)
});
const ticketText = computed(() => {
  t('event.ticketType0') ;
  t('event.ticketType1') ;
  t('event.ticketType2') ;
  t('event.ticketType3') ;
});
</script>

<style scoped>
.card {
  max-height: 300px; /* 設置卡片的最大高度 */
  overflow-y: auto; /* 內容超出時允許垂直滾動 */
}

.content-wrapper {
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 17px;
  /* 調整字體大小 */
  line-height: 1.75;
  /* 增加行距 */
  color: #313131;
}
.title{
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 17px;
  /* 調整字體大小 */
  line-height: 1.75;
  /* 增加行距 */
  color: #313131;
}
.text{
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 16px;
  /* 調整字體大小 */
  line-height: 1.5;
  /* 增加行距 */
  color: #313131;
}

.bold {
  font-weight: bold;
  /* 設置粗體 */
}

.divider{
  margin-top: 10px;
  margin-bottom: 15px;
  color: #555555;
}
</style>