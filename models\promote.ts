import { Profile } from "./user";

export interface PromoteImage {
  language: string;
  url: [string];
}

export interface PromoteTeamResult {//新方法 團隊
  bonus: Bonus;
  quantityDetail: QuantityDetail;
  teamList: TeamList[];
  allTimeDetail: TeamDetail[];
  pastYearDetail: TeamDetail[];
  pastMonthDetail: TeamDetail[];
  level: number;
}

export interface PromotePersonalResult {//新方法 個人
  bonus: Bonus;
  quantityDetail: QuantityDetail;
  memberInfo: MemberInfo;
  leader: Leader;
  promoteDetail: PromoteDetail[];
}

export interface Bonus {
  currency: string;
  dollar: number;
  exchangeFor: string;
}

export interface QuantityDetail {
  oneMonth: number;
  sixMonths: number;
  oneYear: number;
}

export interface PromoteDetail {
  customerID: string;
  customerName: string;
  customerPhoto: string;
  orderType: number;
  orderTime: number;
}

export interface MemberInfo {
  isMember: boolean;
  discountCode: string;
  auto_renew_status: boolean;
  startTime: EpochTimeStamp;
  endTime: EpochTimeStamp;
  duration: number;
  level: number;
  webUrl: number;
}

export interface Leader {
  leaderID: string;
  leaderName: string;
  leaderPhoto: string;
}

export interface TeamList {
  teamMemberID: string;
  teamMemberName: string;
  teamMemberPhoto: string;
  joinTime: number;
}

export interface TeamDetail {
  teamMemberID: string;
  teamMemberName: string;
  teamMemberPhoto: string;
  teamMemberTotal: QuantityDetail;
}