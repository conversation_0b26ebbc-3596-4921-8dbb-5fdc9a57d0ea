<script lang="ts" setup>
import { getPromoteImage } from "../../api/promote";
import { globalStore } from "../../stores/global";
import { MemberInfo } from "~~/models/promote";
import { isoToLocalLanguageName } from "~~/util/language";
import { breakPointLevel } from "~~/models/mediaQuery";

// Swiper 跑馬燈套件
import { Swiper, SwiperSlide } from "swiper/vue";
import { Pagination, Autoplay } from "swiper";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/autoplay";

const props = defineProps({
  memberInfo: {
    type: Object as () => MemberInfo,
    default: null,
  },
  Level: {
    type: Number,
    default: 1
  }
});

const store = globalStore();
let loading = ref(true);
let images = ref([]);
let currentImages = ref([]);
let currentLanguage = ref("zh");

const onLanguageChanged = async (language: String) => {
  currentImages.value = images.value.filter(
    (batch) => batch.language == language
  )[0].url;
};

onMounted(async () => {
  images.value = await getPromoteImage(store.getCurrentUser.userID);
  // 推廣預設語言為en，若有使用者當下語言的推廣圖片，則用使用者語言取代
  const imgLanguages = images.value.map((e) => e.language);
  if (imgLanguages.includes(store.getLanguage)) {
    currentLanguage.value = store.getLanguage;
    onLanguageChanged(currentLanguage.value);
  }
  loading.value = false;
});
</script>
<template>
  <el-card shadow="always" v-loading="loading">
    <el-row class="el-row" type="flex">
      <el-col>
        <el-descriptions
          direction="vertical"
          :title="$t('promote.promote_description')"
        >
          <el-descriptions-item
            >{{ $t("promote.promote_description_1") }}
            <br />
            {{ $t("promote.promote_description_2", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promote_description_3", { month: 1 }) }}
          </el-descriptions-item>
        </el-descriptions>
        <br/>
        <el-descriptions v-if="Level===2"
          direction="vertical" 
          :title="$t('promote.promoteTeam_description')"
        >
          <el-descriptions-item 
            >{{ $t("promote.promoteTeam_description_1") }}
            <br />
            {{ $t("promote.promoteTeam_description_2") }}
            <br />
            {{ $t("promote.promoteTeam_description_3", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promoteTeam_description_4", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promoteTeam_description_5") }}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions v-if="Level===3"
          direction="vertical" 
          :title="$t('promote.promoteTeam_description')"
        >
          <el-descriptions-item 
            >{{ $t("promote.promoteTeam_description_6") }}
            <br />
            {{ $t("promote.promoteTeam_description_7") }}
            <br />
            {{ $t("promote.promoteTeam_description_8") }}
            <br />
            {{ $t("promote.promoteTeam_description_9", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promoteTeam_description_10", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promoteTeam_description_11", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promoteTeam_description_12", { percentage: 10 }) }}
            <br />
            {{ $t("promote.promoteTeam_description_13") }}
          </el-descriptions-item>
        </el-descriptions>
      </el-col>
    </el-row>
    <el-divider></el-divider>
    <el-skeleton v-if="memberInfo == null" :rows="5" animated />
    <el-row v-else type="flex" :gutter="32">
      <el-col :span="12">
        <el-space
          :fill="true"
          direction="vertical"
          :style="{ 'align-items': 'start', width: '100%' }"
        >
          <div>{{ $t("promote.your_promote_code") }}</div>
          <div class="duration-bg">
            <text class="duration-text">
              {{ memberInfo.discountCode }}
            </text>
          </div>
        </el-space>
      </el-col>
      <el-col :span="12">
        <el-space
          :fill="true"
          direction="vertical"
          :style="{ 'align-items': 'start', width: '100%' }"
        >
          <div>{{ $t("promote.image_language") }}</div>
          <el-select
            @change="onLanguageChanged"
            v-model="currentLanguage"
            class="m-2"
            placeholder="Select"
          >
            <el-option
              v-for="item in images"
              :key="item.language"
              :label="isoToLocalLanguageName(item.language)"
              :value="item.language"
            />
          </el-select>
        </el-space>
      </el-col>
      <el-col>
        <swiper
          :slidesPerView="store.getScreenSize == breakPointLevel[0] ? 1 : 2"
          :centeredSlides="true"
          :spaceBetween="15"
          :grabCursor="true"
          :pagination="{
            clickable: true,
          }"
          :autoplay="{
            delay: 4000,
            disableOnInteraction: false,
          }"
          :modules="[Pagination, Autoplay]"
        >
          <swiper-slide v-for="url in currentImages" :key="url"
            ><img :src="url"
          /></swiper-slide>
        </swiper>
      </el-col>
    </el-row>
  </el-card>
</template>

<style scoped>
.el-col {
  margin-bottom: 32px;
}
.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
