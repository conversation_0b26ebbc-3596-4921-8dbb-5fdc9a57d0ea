<template>
  <el-button :style="{ minWidth: '150px', fontSize: '16px', color: '#111111' }" type="info" plain size="large"
    @click=handleEcpayClick>{{ buttonText }} </el-button>
    <MainLogin
      :visible=isDialogVisible
      @update:visible="isDialogVisible = $event"
    />
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { useRouter } from 'vue-router';

const router = useRouter();
const props = defineProps({
  cost: {//價格
    type: Number,
    required: true,
  },
  language: {//未登入時重導向用
    type: String,
    required: true,
  }
});
const store = globalStore();
const { t } = useI18n();
const buttonText = computed(() => {
  return `${t('event.register')} TWD ${props.cost}$`;
});
const isDialogVisible = ref(false);

async function handleEcpayClick(event: MouseEvent) {//這個組件是假的 與綠界無關 導向login
  event.stopPropagation();
  console.log("假的按鈕 帶你去登入")
  isDialogVisible.value = true;
}
</script>

<style scoped>
.el-button {
  border-radius: 8px;
  /* 圓角 */
}
</style>