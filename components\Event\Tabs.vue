<template>
  <div class="tags-div">
    <el-tabs v-model="selectTag" class="event-tabs" stretch="true">
      <el-tab-pane :label="$t('event.all')" name=""></el-tab-pane>
      <el-tab-pane v-for="tag in eventTags" :key="tag.tagID" :label="tag.label" :name="tag.tagID"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { getEventTags } from '../../api/event';
import { getGuestKey } from '../../api/user';
import { globalStore } from '../../stores/global';
import { EventTags } from '~~/models/event';
import { User } from '~~/models/user';

const route = useRoute();
const language = ref(route.query.language || 'default');
const store = globalStore();
const eventTags = ref<EventTags[]>([]);
const selectTag = ref('');
let user: User;

const emit = defineEmits(['update:selectTag']);

onMounted(async () => {
  await getEventTagsList();//取得活動標籤
});

watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  if(language.value != 'default')
    await getEventTagsList();
});

watch(() => selectTag.value, async () => {//切換標籤時 回傳資料
  emit('update:selectTag', selectTag.value);
});


async function getEventTagsList() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  if (user == null) {
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    eventTags.value = await getEventTags({guestKey: guestKey, language: languageString});
  } else {//已登入 直接抓系統語言
    eventTags.value = await getEventTags();
  }
}
</script>

<style scoped>
.event-tabs {
  color: #555555;
  font-size: 32px;
  font-weight: 600;
  --el-tabs-header-height: 50px;
}

.el-tabs.el-tabs--top.event-tabs {
  background-color: #ffffff;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 10px;
}
</style>