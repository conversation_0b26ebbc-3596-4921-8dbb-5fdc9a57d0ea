<script lang="ts" setup>
import {
  Location,
  Document,
  Menu as IconMenu,
  Setting,
} from "@element-plus/icons-vue";
import { globalStore } from "../../stores/global";
import { User } from "~~/models/user";
import { getInAppPayMemberInfo } from "~~/api/inAppPay";
import { BrandCheck } from "~~/models/brand";
import { checkBrand } from "~~/api/brand";
const store = globalStore();
const config = useRuntimeConfig();
const emit = defineEmits<{
  (e: "onMenuItemSeleted"): void;
}>();
let user: User;
const loggedIn = ref(false);
const notLoggedIn = ref(true);//直接設定show:!loggedIn不管用 只好開兩個變量
const isMember = ref(false);
const brandCheck = ref<BrandCheck>();//是否是品牌方
const brandID = ref('');
const loading = ref(true);
const menuItems = ref([//需要注意 key應該形如/home(絕對路徑) 而非home(相對路徑) 不然從動態路由跳轉時會發生錯誤
  {
    key: "/home",
    name: "menu.home",
    src: "/_icons_/home-icon.svg",
    show: false,
  },
  {
    key: "/teacher",
    name: "menu.teacher",
    src: "/_icons_/teacher-icon.svg",
    show: false,
  },
  {
    key: "/course",
    name: "menu.course",
    src: "/_icons_/course-icon.svg",
    show: false,
  },
  {
    key: "/teachingRecord",
    name: "menu.teaching_record",
    src: "/_icons_/teaching-record-icon.svg",
    show: false,
  },
  {
    key: "/message",
    name: "menu.message",
    src: "/_icons_/message-icon.svg",
    show: false,
  },
  {
    key: "/ranking",
    name: "menu.course_ranking",
    src: "/_icons_/ranking-icon.svg",
    show: false,
  },
  {
    key: "/event",
    name: "menu.event",
    src: "/_icons_/course-icon.svg",
    show: true,//isMember
  },
  {
    key: "/promote",
    name: "menu.promote",
    src: "/_icons_/promote-icon.svg",
    show: false,//isMember
  },
  {
    key: "/brand",
    name: "menu.brand",
    src: "/_icons_/teacher-icon.svg",
    show: true,
  },
  {
    key: '',//先留空 等抓到資料在補
    name: "menu.brandEdit",
    src: "/_icons_/brandEdit-icon.svg",
    show: false,//先不顯示
  },
  {
    key: "/home",
    name: "menu.toAPP",
    src: "/_icons_/app-icon.svg",
    show: true,
  },
  {
    key: "/account",
    name: "menu.account",
    src: "/_icons_/account-icon.svg",
    show: loggedIn,
  },

  {
    key: "/login",
    name: "menu.login",
    src: "/_icons_/logout-icon.svg",
    show: notLoggedIn,
  },

]);
const handleSelect = async (key: string, keyPath: string[]) => {
  console.log(key, keyPath);
  emit("onMenuItemSeleted");
  store.setCurrentRoute(key);
  console.log(store.getCurrentRoute);
  navigateTo(store.getCurrentRoute);

};

onMounted(async () => {
  //isMember.value = (await getInAppPayMemberInfo()).isMember;

  user = store.getCurrentUser;
  if (user != null) {
    loggedIn.value = true;
    notLoggedIn.value = false;

    brandCheck.value = await checkBrand();//有登入才會做品牌身分驗證
    brandID.value = brandCheck.value.brandWebIndex || brandCheck.value.brandID || '';
    const brandEditItem = menuItems.value.find(item => item.name === 'menu.brandEdit');
    if (brandEditItem && brandID.value) {
      brandEditItem.key = `/brandEdit/${brandID.value}`;
      brandEditItem.show = true; // 確保顯示
    }
  }
  loading.value = false;
  console.log(config.version);
});
</script>

<template>
  <div>
    <el-menu v-loading="loading" :style="{ height: '100%' }" :unique-opened="true"
      :default-active="store.getCurrentRoute" @select="handleSelect">
      <template v-for="item in menuItems">
        <el-menu-item v-if="item.show" :key="item.key" :index="item.key">
          <el-icon>
            <img class="icon" :class="{ 'is-focus': store.getCurrentRoute === item.key }" :src="item.src" />
          </el-icon>
          <span class="text-itemName">{{ $t(item.name) }}</span>
        </el-menu-item>
      </template>
    </el-menu>
    <slot></slot>
    <div class="bottomleft">{{ config.public.version }}</div>
  </div>
</template>

<style>
.icon {
  width: 18px;
  height: 18px;
}

.icon.is-focus {
  filter: invert(100%) sepia(100%) saturate(1%) hue-rotate(1deg) brightness(114%) contrast(101%);
}

.el-menu-item.is-active {
  color: #fff;
  background-color: #4e80c2;
}

.bottomleft {
  position: absolute;
  width: 200px;
  height: 45px;
  background-color: transparent;
  bottom: 0px;
  left: 5px;
  filter: invert(66%) sepia(88%) saturate(1%) hue-rotate(357deg) brightness(111%) contrast(102%);
}

.text-itemName {
  /* 設置字型 */
  font-size: 16px;
  /* 字型大小 */
  line-height: 1;
  /* 行距 */
  margin-left: 20px;
  /* 上下左右邊距 */
  color: #555555;
  /* 字體顏色 */
  font-weight: bold;
  /* 粗體 */
  letter-spacing: 1px;
  /* 字距 */
}

@media (min-width: 470px) {
  .text-introContent {
    display: none;
  }
}
</style>
