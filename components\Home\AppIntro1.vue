<script setup lang="ts">
definePageMeta({
  middleware: ["unauth"],
});
const googlePlayUrl = "https://play.google.com/store/apps/details?id=lab.italkutalk";
const appStoreUrl = "https://apps.apple.com/tw/app/italkutalk/id1263409577";
const props = defineProps({
  language: {//圖片語言
    type: String,
    required: true,
  },
});
</script>

<template>
  <div class="appIntro-page">
    <!-- APP 下載卡片 -->
    <el-card class="appDownload-card">
      <div class="appDownload-content">
        <div class="text-content">
          <span>{{ $t('home.appIntro1B1') }}</span>
          <span>{{ $t('home.appIntro1B2') }}</span>
          <span>{{ $t('home.appIntro1B3') }}</span>
        </div>
        <div class="image-content">
          <a :href="googlePlayUrl" target="_blank">
            <img :src="`https://itut-website.s3.ap-northeast-1.amazonaws.com/public/appIntro/appIntro1l_${props.language}.png`" alt="App Logo" class="app-logo left" />
          </a>
          <a :href="appStoreUrl" target="_blank">
            <img :src="`https://itut-website.s3.ap-northeast-1.amazonaws.com/public/appIntro/appIntro1r_${props.language}.png`" alt="App Logo" class="app-logo right" />
          </a>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.appIntro-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0px 20px 0px 20px;
}

.appDownload-card {
  padding: 20px;
  background: linear-gradient(to bottom, #ffffff, #daecf9);
  /* 背景漸層 */
}

.appDownload-content {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
}

.text-content {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  color: #27465e;
  /* 文字內文顏色 */
  font-weight: bold;
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.5;
  /* 行距 */
  font-size: 24px;
  /* 字體大小 */
  margin-left: 65px;
}

.image-content {
  max-width: 535px;
  max-height: 535px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  /* 確保裡面的物件不要超出這個 div */
}

.app-logo {
  width: calc(100% - 5px);
  /* 每張圖片占容器寬度的一半 */
  height: auto;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  /* 保持比例並確保圖片在容器內完整顯示 */
}

.app-logo.left {
  margin-right: 10px;
  /* 左邊圖片的右邊間距 */
}

@media (max-width: 500px) {
  .image-content {
    justify-content: center;
    text-align: center;
  }

  .appIntro-page {
    padding: 0px;
  }
}

@media (max-width: 800px) {
  .appDownload-content {
    flex-direction: column;
  }

  .image-content {
    justify-content: center;
    text-align: center;
  }

  .text-content {
    justify-content: center;
    text-align: center;
    margin-left: 0px;
  }
}

@media (min-width: 1500px) {
  .appIntro-page {
    padding-right: 100px;
  }
}

@media (min-width: 1600px) {
  .appIntro-page {
    padding-right: 200px;
  }

  .text-content {
    font-size: 28px;
  }
}

@media (min-width: 1700px) {
  .appIntro-page {
    padding-right: 300px;
  }
}
</style>