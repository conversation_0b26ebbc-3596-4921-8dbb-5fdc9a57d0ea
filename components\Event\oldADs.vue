<template>
  <el-button :style="{ minWidth: '150px', fontSize: '16px', color: '#111111' }" type="info" plain size="large"
    @click=handleADsClick>{{ buttonText }} </el-button>
    <el-dialog :modelValue="isADsVisible" custom-class="toAPP-dialog" :before-close="handleClose" append-to-body>
    <div class="dialog-content">
      <!-- 第一行 -->
      <div class="ADs-section">
        <img :src="`/ad_${language}.png`" alt="Logo" class="ADs">
      </div>
      <div class="store-section">
        <a :href="googlePlayUrl" target="_blank">
          <img src="/googlePlay.png" alt="Google Play" class="google-play-logo">
        </a>
        <a :href="appStoreUrl" target="_blank">
          <img src="/appStore.png" alt="App Store" class="app-store-logo">
        </a>
      </div>
      <div class="slogan-section">
        <EventEcpay :eventID=props.eventID :cost=props.cost :ticketTypeID=props.ticketTypeID :isApply=props.isApply :buttonType=2 :buttonText=2>
        </EventEcpay>
      </div>
    </div>
  </el-dialog>
  
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { useRoute } from 'vue-router';
const props = defineProps({
  eventID: {//活動ID
    type: String,
    required: true,
  },
  ticketTypeID: {//哪一種票價
    type: Number,
    required: true,
  },
  isApply: {//是否已購買
    type: Boolean,
    required: true,
  },
  cost: {//價格
    type: Number,
    required: true,
  }
});
const { t } = useI18n();
const isADsVisible = ref(false);
const googlePlayUrl="https://play.google.com/store/apps/details?id=lab.italkutalk";
const appStoreUrl="https://apps.apple.com/tw/app/italkutalk/id1263409577";
const buttonText = computed(() => {
  return props.isApply ? t('event.registered') : `${t('event.register')}(TWD ${props.cost}$)`;
});
const route = useRoute();
let language = ref(route.query.language || 'default');
async function handleADsClick(event: MouseEvent) {//點擊按鈕時 跳出廣告
  event.stopPropagation();
  isADsVisible.value = true;
  console.log("點了")
};

// 關閉 dialog 的方法
function handleClose(done: () => void ) {
  isADsVisible.value = false;
  done();
};
watch(
  () => route.query.language,
  (newLanguage) => {
    language.value = newLanguage || 'default';
  }
);
</script>

<style scoped>
.el-button {
  border-radius: 8px;
  /* 圓角 */
}
.ADs-section {
  display: flex;
  align-items: center;
  justify-content: center;
}
.ADs {
  max-width: 460px;
  max-height: 460px;
  margin-right: 0px;
}

</style>