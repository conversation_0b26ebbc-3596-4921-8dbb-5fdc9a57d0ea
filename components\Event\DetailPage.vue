<template>
  <div v-if="eventDetail" class="event-detail-page">
    <el-card class="box-card">
      <div v-if="eventDetail.pastPhotoList && eventDetail.pastPhotoList.length > 0" ref="carouselContainer">
        <el-carousel :height="carouselHeight" arrow="always">
          <el-carousel-item v-for="(photo, index) in allPhotos" :key="index">
            <div class="image-wrapper">
              <img :src="photo" class="image" alt="活動照片" />
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div v-else class="image-wrapper">
        <img :src="eventDetail?.photo" class="image" alt="活動照片" />
      </div>
      <div class="content-wrapper">
        <div>
          <span class="bold">{{ $t("event.theme") }}：</span>
          <span v-if="isTrans">{{ eventDetail?.transEventDetail.name }}</span>
          <span v-if="!isTrans">{{ eventDetail?.oriEventDetail.name }}</span>
        </div>
        <div v-if="eventDetail?.oriTeacherDetail.name" @click="(event: any) => handleTeacherClick(event, eventDetail!)">
          <span class="bold">{{ $t("event.teacher") }}：</span>
          <span class="clickable-text" v-if="isTrans">{{ eventDetail?.transTeacherDetail.name }}</span>
          <span class="clickable-text" v-if="!isTrans">{{ eventDetail?.oriTeacherDetail.name }}</span>
        </div>
        <div @click="(event: any) => handleBrandClick(event, eventDetail!)">
          <span class="bold">{{ $t("event.brand") }}：</span>
          <span class="clickable-text" v-if="isTrans">{{ eventDetail?.transBrandDetail.name }}</span>
          <span class="clickable-text" v-if="!isTrans">{{ eventDetail?.oriBrandDetail.name }}</span>
        </div>
        <div v-if="eventDetail?.storeName">
          <span class="bold">{{ $t("event.location") }}：</span>{{ eventDetail?.storeName }}
        </div>
        <div><span class="bold">{{ $t("event.address") }}：</span>{{ eventDetail?.address }}</div>
        <div><span class="bold">{{ $t("event.eventTime") }}：</span>{{ formatEventTime(eventDetail?.localStartTime ?? '',
    eventDetail?.localEndTime ?? '', eventDetail?.week ?? 0) }}</div>
        <div v-if="eventDetail.ticketType && eventDetail.ticketType.length > 0">
          <div
            v-if="!eventDetail?.paymentTicket || eventDetail.paymentTicket.ticketTypeID === 2 || eventDetail.paymentTicket.ticketTypeID === 3">
            <span class="bold">{{ $t("event.earlyBirdPrice") }}：</span>
            <span>{{ $t("event.before1") }}{{ formatDate(eventDetail?.localEarlyBirdEndTime ?? '') }}{{
    $t("event.before2") }}，</span>
            <span>{{ $t("event.member") }} TWD {{ selectedTicketPrice(3, false) }}$</span>
            <span v-if="selectedTicketPrice(3, true)">({{ $t("event.productPrice") }} TWD {{ selectedTicketPrice(3,
    true) }}$)</span>
            <span>，{{ $t("event.nonMember") }} TWD {{ selectedTicketPrice(2, false) }}$</span>
            <span v-if="selectedTicketPrice(2, true)">({{ $t("event.productPrice") }} TWD {{ selectedTicketPrice(2,
    true) }}$)</span>
            <span v-if="eventDetail?.oriEventDetail.meals">，</span>
            <span v-if="isTrans">{{ eventDetail?.transEventDetail.meals }}</span>
            <span v-if="!isTrans">{{ eventDetail?.oriEventDetail.meals }}</span>
          </div>
          <div>
            <span class="bold">{{ $t("event.generalAdmission") }}：</span>
            <span>{{ $t("event.member") }} TWD {{ selectedTicketPrice(1, false) }}$</span>
            <span v-if="selectedTicketPrice(1, true)">({{ $t("event.productPrice") }} TWD {{ selectedTicketPrice(1,
    true) }}$)</span>
            <span>，{{ $t("event.nonMember") }} TWD {{ selectedTicketPrice(0, false) }}$</span>
            <span v-if="selectedTicketPrice(0, true)">({{ $t("event.productPrice") }} TWD {{ selectedTicketPrice(0,
    true) }}$)</span>
            <span v-if="eventDetail?.oriEventDetail.meals">，</span>
            <span v-if="isTrans">{{ eventDetail?.transEventDetail.meals }}</span>
            <span v-if="!isTrans">{{ eventDetail?.oriEventDetail.meals }}</span>
          </div>
        </div>
        <div v-if="eventDetail?.oriEventDetail.description">
          <span class="bold">{{ $t("event.introduction") }}：</span>
          <span v-if="isTrans">{{ eventDetail.transEventDetail.description }}</span>
          <span v-if="!isTrans">{{ eventDetail.oriEventDetail.description }}</span>
        </div>
        <div v-if="eventDetail?.oriTeacherDetail.description">
          <span class="bold">{{ $t("event.teacherIntroduction") }}：</span>
          <span v-if="isTrans">{{ eventDetail.transTeacherDetail.description }}</span>
          <span v-if="!isTrans">{{ eventDetail.oriTeacherDetail.description }}</span>
        </div>
        <div v-if="eventDetail?.oriBrandDetail.description">
          <span class="bold">{{ $t("event.brandIntroduction") }}：</span>
          <span v-if="isTrans">{{ eventDetail.transBrandDetail.description }}</span>
          <span v-if="!isTrans">{{ eventDetail.oriBrandDetail.description }}</span>
        </div>
        <div v-if="eventDetail?.oriEventDetail.storeDescription">
          <span class="bold">{{ $t("event.locationIntroduction") }}：</span>
          <span v-if="isTrans">{{ eventDetail.transEventDetail.storeDescription }}</span>
          <span v-if="!isTrans">{{ eventDetail.oriEventDetail.storeDescription }}</span>
        </div>
        <div v-if="eventDetail?.oriEventDetail.feature">
          <span class="bold">{{ $t("event.eventFeatures") }}：</span>
          <span v-if="isTrans">{{ eventDetail.transEventDetail.feature }}</span>
          <span v-if="!isTrans">{{ eventDetail.oriEventDetail.feature }}</span>
        </div>
        <EventTrans v-if="eventDetail?.oriLanguage != language" @update:isTrans="isTrans = $event"
          :text="'brand.detail'"></EventTrans>
        <div v-if="!eventDetail.isEnd && props.isPlatformEvent" class="button-container">
          <template v-if="(eventDetail?.totalQuantity ?? 0) <= (eventDetail?.participants ?? 0)">
            <el-button disabled :style="{ minWidth: '150px', fontSize: '16px', color: '#FF0000', borderRadius: '8px' }"
              type="info" plain size="large" @click=handleDoNotThing>{{ $t("event.full") }} </el-button>
          </template>
          <template v-else-if="user === null">
            <EventFakeEcpay :cost="eventDetail?.paymentTicket.price" :language="language"></EventFakeEcpay>
          </template>
          <template v-else>
            <template
              v-if="(eventDetail?.paymentTicket.ticketTypeID === 1 || eventDetail?.paymentTicket.ticketTypeID === 3)">
              <EventEcpay :eventID=eventDetail?.eventID :cost=eventDetail?.paymentTicket.price
                :ticketTypeID=eventDetail?.paymentTicket.ticketTypeID :isApply=eventDetail?.isApply :button-text=1
                :button-type=1>
              </EventEcpay>
            </template>
            <template v-else>
              <EventADs :eventID=eventDetail?.eventID :cost=eventDetail?.paymentTicket.price
                :ticketTypeID=eventDetail?.paymentTicket.ticketTypeID :isApply=eventDetail?.isApply>
              </EventADs>
            </template>
          </template>
          <EventGoogleMap :storeName=eventDetail?.storeName :location=eventDetail?.location></EventGoogleMap>
          <EventShare :eventID=props.eventID :language=language></EventShare>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getEventDetail, formatDate, formatEventTime } from "../../api/event";
import { getGuestKey } from "../../api/user";
import { EventDetail } from "~~/models/event";
import { User } from "~~/models/user";
import { globalStore } from "../../stores/global";
import { useI18n } from "vue-i18n";
import { useRouter } from 'vue-router';
const router = useRouter();
const { t } = useI18n()
const route = useRoute();
const store = globalStore();
const loading = ref(true);
const isTrans = ref(true);
const eventDetail = ref<EventDetail>();
let user: User;
const language = ref(route.query.language || 'default');
const carouselHeight = ref('0px');
const props = defineProps({
  eventID: {//這邊傳進來有可能是eventID 有可能其實是eventWebIndex 要注意
    type: String,
    required: true,
  },
  isPlatformEvent: {//是否是平台上舉辦的活動，切換call不同API
    type: Boolean,
    required: true,
  }

});
const allPhotos = computed(() => [eventDetail.value!.photo, ...eventDetail.value!.pastPhotoList.map(photo => photo.url)]);
onMounted(async () => {//進入畫面時
  await getDetail();//取得活動詳情
  loading.value = false;
  watchEffect(() => {//寫在這裡而非index.vue 或是上面一點 是因為要等eventDetail有值之後才能call
    useHead({
      title: `${eventDetail.value?.oriEventDetail.name ?? ''} - ${t('home.title')}`,
    });
  });
  getCardWidth();
});
async function getDetail() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  console.log(user);
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    eventDetail.value = await getEventDetail({ eventID: props.eventID, guestKey: guestKey, language: languageString, isPlatformEvent: props.isPlatformEvent });
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    eventDetail.value = await getEventDetail({ eventID: props.eventID, isPlatformEvent: props.isPlatformEvent });
  }
}
function handleTeacherClick(event: MouseEvent, eventDetail: EventDetail) {
  if (eventDetail.teacherID) { // 存在教師ID
    const teacherIndex = eventDetail.teacherWebIndex || eventDetail.teacherID;
    router.push(`/teacher/${teacherIndex}?language=${language}`);
  }
}
function handleBrandClick(event: MouseEvent, eventDetail: EventDetail) {
  if (eventDetail.brandID) { // 存在品牌ID
    const brandIndex = eventDetail.brandWebIndex || eventDetail.brandID;
    router.push(`/${brandIndex}?language=${language}`);
  }
}
function handleDoNotThing(event: MouseEvent) {//點擊卡片時
  event.stopPropagation();
}
watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  loading.value = true; // 設置加載狀態
  getDetail(); // 重新獲取活動詳情
  loading.value = false; // 加載完成
});
const selectedTicketPrice = (ticketTypeID: number, origin: boolean) => {
  if (!eventDetail.value) {
    return 0;
  }
  const selectedTicket = eventDetail.value.ticketType.find(ticket => ticket.ticketTypeID === ticketTypeID);
  if (origin) {
    return selectedTicket ? selectedTicket.originalPrice : 0;
  }
  return selectedTicket ? selectedTicket.price : 0;
};
//#region 自動計算跑馬燈組件高度
const carouselContainer = ref<HTMLElement | null>(null);
const getCardWidth = () => {
  if (carouselContainer.value) {
    const cardWidth = carouselContainer.value.clientWidth;
    carouselHeight.value = `${(cardWidth / 16) * 9}px`;
    console.log('Card Width:', carouselHeight.value);
  } else {
    console.error('carouselContainer is null');
  }
};

//#endregion
</script>

<style scoped>
.event-detail-page {
  padding: 0px;
}

.image-wrapper {
  width: 100%;
  aspect-ratio: 16 / 9;
  /* 設置圖片的寬高比 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}

.content-wrapper {
  padding: 26px;
}

.box-card {
  margin: 20px;
}

.el-card {
  border-radius: 10px;
  /* 卡片的圓角 */
  overflow: hidden;
  /* 確保圓角效果顯示 */
  --el-card-padding: 0;
}

.el-card__body {
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 16px;
  /* 調整字體大小 */
  line-height: 1.6;
  /* 增加行距 */
}

.bold {
  font-weight: bold;
  /* 設置粗體 */
}

.clickable-text {
  color: #1A73E8;
  text-decoration: underline;
  cursor: pointer;
}

.content-wrapper div {
  margin-bottom: 10px;
  /* 增加段落間距 */
}

.button-container {
  display: flex;
  gap: 5px;
  /* 按鈕之間的間距 */
  margin-top: 10px;
  /* 與上方組件的間距 */
}

.button-container :deep(.el-button + .el-button) {
  margin-left: 0 !important;
  /* 覆蓋 Element Plus 預設的 margin-left */
}

@media (max-width: 500px) {
  .box-card {
    margin: 0px;
  }

  .el-card {
    border-radius: 0px;
  }
}
</style>