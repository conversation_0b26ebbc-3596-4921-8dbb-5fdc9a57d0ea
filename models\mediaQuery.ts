export interface ScreenSize {
  height: number;
  width: number;
}

export const dimensionLevel = [576, 768, 992, 1200, 1400];
export const breakPointLevel = ["xs", "sm", "md", "lg", "xl", "xxl"];
export const getBreakpointLevel = (dimension: number) => {
  let level = 0;
  for (let i = 0; i < dimensionLevel.length; i++) {
    const value = dimensionLevel[i];
    if (dimension < value) return breakPointLevel[i];
  }
  return "xxl";
};
