<script setup>
import { globalStore } from "../../stores/global";
import { onMounted } from "vue";

definePageMeta({
  layout: ["empty"],
});
let store = globalStore();
const router = useRouter();
const userName = ref(undefined);
onMounted(async () => {
  const route = useRoute();
  const code = route.query.code;
  const language = store.getCacheLanguage;
  if (code === undefined) {
    router.push("/");
    return;
  }
  
  try {
    // 透過code向web server取得FB的用戶資料
    // 流程可以參考FB文件: https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow/
    const { data } = await useFetch("/api/auth/facebook", {
      query: { code: code },
    });
    console.log(data.value);
    userName.value = data.value.name;
    // 使用FB登入
    await store.signInWithFb(data.value.id, data.value.name);
    router.push("/event");
    store.setRedirect(undefined);
  } catch (error) {
    router.push("/login");
  }
});
</script>

<template>
  <div v-if="userName">facebook user: {{ userName }}</div>
  <div v-else>login...</div>
</template>

<style></style>
