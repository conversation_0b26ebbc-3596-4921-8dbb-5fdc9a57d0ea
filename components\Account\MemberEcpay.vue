<template>
  <div @click="handleBuyClick" class="clickable-image">
    <img :src="`https://itut-website.s3.ap-northeast-1.amazonaws.com/public/AD/webADs_${props.language}.png`" alt="App ADs" class="webADsImage" />
  </div>
  <el-dialog custom-class="your-class-name" :width="'340px'" v-model="buyMemberVisible" :title="$t('account.buyMember')"
    @close="handleClose">
    <div class="buttonLine">
      <div class="container">
        <div class="card" @click=handleMemberEcpayClick(1)>
          <div class="blue-background">
            <p class="blue-text">{{ $t('account.easyTry') }}</p>
            <p class="blue-text">{{ $t('account.buy1Month') }}</p>
            <br />
            <p class="black-text">300 TWD</p>
            <p class="blue-text">(10 {{ $t('account.usd') }})</p>
          </div>
          <p>{{ $t('account.average') }}</p>
          <p class="black-text">300 TWD/{{ $t('account.month') }}</p>
          <p class="blue-text">(10{{ $t('account.usd') }}/{{ $t('account.month') }})</p>
          <hr />
          <p>{{ $t('account.average') }}</p>
          <p class="black-text">10 TWD/{{ $t('account.day') }}</p>
          <p class="blue-text">(0.33{{ $t('account.usd') }}/{{ $t('account.day') }})</p>
        </div>
        <div class="card" @click=handleMemberEcpayClick(6)>
          <div class="red-background">
            <p class="red-text">{{ $t('account.mostPopular') }}</p>
            <p class="red-text">{{ $t('account.buy6Months') }}</p>
            <br />
            <p class="black-text">600 TWD</p>
            <p class="red-text">(20 {{ $t('account.usd') }})</p>
          </div>
          <p>{{ $t('account.average') }}</p>
          <p class="black-text">100 TWD/{{ $t('account.month') }}</p>
          <p class="red-text">(3.3{{ $t('account.usd') }}/{{ $t('account.month') }})</p>
          <hr />
          <p>{{ $t('account.average') }}</p>
          <p class="black-text">3.3 TWD/{{ $t('account.day') }}</p>
          <p class="red-text">(0.11{{ $t('account.usd') }}/{{ $t('account.day') }})</p>
        </div>
        <div class="card" @click=handleMemberEcpayClick(12)>
          <div class="yellow-background">
            <p class="yellow-text">{{ $t('account.bestPrice') }}</p>
            <p class="yellow-text">{{ $t('account.buy12Months') }}</p>
            <br />
            <p class="black-text">900 TWD</p>
            <p class="yellow-text">(30 {{ $t('account.usd') }})</p>
          </div>
          <p>{{ $t('account.average') }}</p>
          <p class="black-text">75 TWD/{{ $t('account.month') }}</p>
          <p class="yellow-text">(2.5{{ $t('account.usd') }}/{{ $t('account.month') }})</p>
          <hr />
          <p>{{ $t('account.average') }}</p>
          <p class="black-text">2.5 TWD/{{ $t('account.day') }}</p>
          <p class="yellow-text">(0.08{{ $t('account.usd') }}/{{ $t('account.day') }})</p>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { memberPaymentCreate } from "../../api/user";
import { PaymentCreateResult } from "~~/models/event";
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { ElMessage } from 'element-plus'
const store = globalStore();
const i18n = useI18n();
const paymentCreateResult = ref<PaymentCreateResult>();
const config = useRuntimeConfig();
const buyMemberVisible = ref(false);
async function handleBuyClick() {//點擊按鈕時 購買會員選項
  if (props.isMember) {
    ElMessage({
      message: i18n.t('account.isMember'),
      type: 'success',
    })
  }
  else {
    buyMemberVisible.value = true;
    console.log("點了")
  }
};
const props = defineProps({
  isMember: {//是否是會員
    type: Boolean,
    required: true,
  },
  language: {//語言
    type: String,
    required: true,
  }
});
// 關閉 dialog 的方法
function handleClose() {
  buyMemberVisible.value = false;
};

async function handleMemberEcpayClick(duration: number) {//點擊按鈕時
  paymentCreateResult.value = await memberPaymentCreate(duration);
  console.log(paymentCreateResult.value);
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = `${config.public.ecpayUrl}`;
  //form.action = 'https://payment-stage.ecpay.com.tw/Cashier/AioCheckOut/V5';
  form.style.display = 'none';
  form.target = '_blank'; // 在新視窗中打開
  // 將 paymentCreateResult 的值動態添加到表單中
  let fields = [
    { name: 'MerchantID', value: paymentCreateResult.value.MerchantID },
    { name: 'MerchantTradeNo', value: paymentCreateResult.value.MerchantTradeNo },
    { name: 'MerchantTradeDate', value: paymentCreateResult.value.MerchantTradeDate },
    { name: 'PaymentType', value: paymentCreateResult.value.PaymentType },
    { name: 'TotalAmount', value: paymentCreateResult.value.TotalAmount.toString() },
    { name: 'TradeDesc', value: paymentCreateResult.value.TradeDesc },
    { name: 'ItemName', value: paymentCreateResult.value.ItemName },
    { name: 'ReturnURL', value: paymentCreateResult.value.ReturnURL },
    { name: 'ChoosePayment', value: paymentCreateResult.value.ChoosePayment },
    { name: 'CheckMacValue', value: paymentCreateResult.value.CheckMacValue },
    { name: 'EncryptType', value: paymentCreateResult.value.EncryptType.toString() },
    { name: 'NeedExtraPaidInfo', value: paymentCreateResult.value.NeedExtraPaidInfo },
  ];
  if (paymentCreateResult.value.Language) {
    fields.push({ name: 'Language', value: paymentCreateResult.value.Language });
  }
  fields.forEach(field => {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = field.name;
    input.value = field.value;
    form.appendChild(input);
  });
  document.body.appendChild(form);
  form.submit();
  document.body.removeChild(form);
};
</script>

<style scoped>
.el-button {
  border-radius: 8px;
  /* 圓角 */
}

.buttonLine {
  display: flex;
  flex-direction: row;
}

.buttonLine button {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #2E70B7;
}

:deep(.el-button + .el-button) {
  margin-left: 0 !important;
  /* 取消 margin-left */
}

.webADsImage {
  max-width: 100%;
  transition: transform 0.3s ease;
  /* 平滑的放大過渡效果 */
  cursor: pointer;
  /* 確保鼠標移到圖片上時顯示為點擊狀態 */
}

.webADsImage:hover {
  transform: scale(1.02);
  /* 當鼠標懸停時放大圖片 */
}

.container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.card {
  width: 95px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  text-align: center;
  margin-right: 7.5px;
  transition: transform 0.3s ease;
  cursor: pointer;
  line-height: 0.8;
  overflow: hidden;
  word-wrap: break-word; 
}

.card:hover {
  transform: scale(1.02);
  /* 當鼠標懸停時放大圖片 */
}

.blue-text {
  color: #2f71b9;
}

.red-text {
  color: #d34467;
}

.yellow-text {
  color: #d39446;
}

.black-text {
  color: black;
}

.blue-background {
  background-color: #C3E3F8;
  color: white;
  padding: 10px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.red-background {
  background-color: #FFD9D7;
  color: white;
  padding: 10px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.yellow-background {
  background-color: #FFE8BA;
  color: white;
  padding: 10px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

hr {
  border: 0;
  height: 1px;
  background: #ddd;
  margin: 10px 0;
}


@media (max-width: 800px) {
  .image-content {
    justify-content: center;
    text-align: center;
  }

  .appIntro-page {
    padding: 0px;
  }
}
</style>