<script lang="ts" setup>
import { PromoteDetail } from "~~/models/promote";
const props = defineProps({
  detail: { type: Object as () => PromoteDetail[], default: null },
  cardTitle: { type: String, default: "promote.personalDetail" },
});
const formatDate = (timestamp: number): string => {//顯示推廣時間
  const date = new Date(timestamp);
  return date.toLocaleString();
};
</script>
<template>
  <el-card shadow="always">
    <div>{{ $t(cardTitle) }}</div>
    <el-skeleton v-if="detail == null" :rows="5" animated />
    <el-space v-else :fill="true" style="width: 100%" :size="16">
      <el-table :data="detail" style="width: 100%">
        <el-table-column :label="$t('promote.cusPhoto')" width="100">
          <template #default="{ row }">
            <img :src="row.customerPhoto" :alt="$t('promote.cusPhoto')" style="width: 70px; height: 70px; object-fit: cover;" />
          </template>
        </el-table-column>
        <el-table-column prop="customerName" :label="$t('promote.cusName')" sortable width="110" />
        <el-table-column prop="orderType" :label="$t('promote.orderType')" sortable width="110">
          <template v-slot="{ row }">
            {{ row.orderType }} month
          </template>
        </el-table-column>
        <el-table-column :label="$t('promote.orderTime')" sortable width="180">
          <template #default="{ row }">
            {{ formatDate(row.orderTime) }}
          </template>
        </el-table-column>
      </el-table>
      
    </el-space>
  </el-card>
</template>
