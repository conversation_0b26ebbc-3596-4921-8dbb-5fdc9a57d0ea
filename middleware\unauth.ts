import { globalStore } from "../stores/global";
import { supportLangs } from "~~/util/language";

let store = globalStore();

export default defineNuxtRouteMiddleware(async (to, from) => {//保留最基本的語言功能處理
  if (to.redirectedFrom) return;
  let toLang = to.query.language as string;
  toLang = toLang && supportLangs.includes(toLang) ? toLang : store.getLanguage;
  to.query.language = toLang;
  store.setLanguage(toLang);
  store.setCurrentRoute(to.path.replace("/", ""));
  return navigateTo(to);
});
