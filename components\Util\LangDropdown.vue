<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { globalStore } from "~~/stores/global";
import { ArrowDown } from "@element-plus/icons-vue";
import { isoToLocalLanguageName, supportLangs } from "~~/util/language";
const router = useRouter();
const route = useRoute();
const i18n = useI18n();
let store = globalStore();
const emit = defineEmits<{
  (e: "onLanguageSeleted"): void;
}>();
const handleCommand = async (command: string) => {
  console.log(command);
  i18n.locale.value = command;
  await store.setLanguage(command);
  const currentRoute = route.fullPath;
  router.push({ path: currentRoute });
  emit("onLanguageSeleted");
};
</script>

<template>
  <el-dropdown @command="handleCommand">
    <span class="el-dropdown-link">
      {{ isoToLocalLanguageName(store.getLanguage) }}
      <el-icon class="el-icon--right"><arrow-down /></el-icon>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <template v-for="lang in supportLangs" :key="lang">
          <el-dropdown-item :command="lang">{{
            isoToLocalLanguageName(lang)
          }}</el-dropdown-item>
        </template>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
