<template>
  <div class="brandEdit-form">
    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandLanguage') }}：
      </template>
      <el-select v-model="brandLanguage" placeholder="Select" style="width: 140px">
        <el-option v-for="lang in supportLangs" :key="lang" :label="$t(`Language.${lang}`)" :value="lang" />
      </el-select>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandName') }}：
      </template>
      <el-input v-model="brandName" :placeholder="$t('edit.inputBrandName')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandDescription') }}：
      </template>
      <el-input v-model="brandDescription" type="textarea" :rows="4"
        :placeholder="$t('edit.inputBrandDescription')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandIcon') }}(1:1)：
      </template>
      <EditUploadImage :oldImageUrl="props.brandDetail.icon" v-model:imageUrl="icon" :type="1" :frontPath="'brand'" :backPath="'icon'"></EditUploadImage>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandPhoto') }}(16:9)：
      </template>
      <EditUploadImage :oldImageUrl="props.brandDetail.photo" v-model:imageUrl="photo" :type="2" :frontPath="'brand'" :backPath="'photo'"></EditUploadImage>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandWebIndex') }}：https://www.italkutalk.com/
      </template>
      <el-input v-model="brandWebIndex" :placeholder="$t('edit.inputBrandWebIndex')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('edit.brandVideoUrl') }}：
      </template>
      <el-input v-model="videoUrl" :placeholder="$t('edit.inputBrandVideoUrl')"></el-input>
    </el-form-item>
    <div v-if="videoUrl" class="iframe-container">
      <iframe :src="'https://www.youtube.com/embed/' + getYoutubeID(videoUrl)" title="YouTube video player"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
    </div>
  </div>
  <el-button type="primary" size="large" @click="save">{{ $t('edit.save') }}</el-button>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { supportLangs } from "~~/util/language";
import { BrandEdit, BrandDetail } from "../../models/brand"
import { editBrand } from "../../api/brand"
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
const props = defineProps<{
  brandID: string;
  brandDetail: BrandDetail;
}>();

const i18n = useI18n();
const brandLanguage = ref('');
const brandName = ref('');
const brandDescription = ref('');
const icon = ref('');
const photo = ref('');
const brandWebIndex = ref('');
const videoUrl = ref('');
const router = useRouter();

onMounted(() => {//初始化 如果brandDetail有值(編輯時) 帶入資料
  brandLanguage.value = props.brandDetail.oriLanguage || '';
  brandName.value = props.brandDetail.name || '';
  brandDescription.value = props.brandDetail.oriDescription || '';
  icon.value = props.brandDetail.icon || '';
  photo.value = props.brandDetail.photo || '';
  brandWebIndex.value = props.brandDetail.brandWebIndex || '';
  videoUrl.value = props.brandDetail.youtubeID
    ? `https://www.youtube.com/watch?v=${props.brandDetail.youtubeID}`
    : '';
});
watch(() => brandLanguage.value, async (newLang) => {//切換編輯語言時會重抓資料
  if (props.brandDetail.oriLanguage && newLang != props.brandDetail.oriLanguage)//如果跟之前的不一樣 清空名字與描述 避免語言錯誤
  {
    brandName.value = '';
    brandDescription.value = '';
  } else {//如果一樣 就再帶入上次資料
    brandName.value = props.brandDetail.name || '';
    brandDescription.value = props.brandDetail.oriDescription || '';
  }
});

const save = async () => {//儲存 會檢查然後上傳、重導向
  if (!brandLanguage.value || !brandName.value || !brandDescription.value ||
    !icon.value || !photo.value || !brandWebIndex.value || brandWebIndex.value.length >= 24) {
    ElMessage.error(i18n.t('edit.cannotBeEmpty'));
    return;
  }
  //#region 自定義網址檢查
  const forbiddenPaths = [
    'account', 'brand', 'brandEdit', 'course', 'event', 'home', 'login',
    'message', 'pastEvent', 'promote', 'ranking', 'register', 'teacher',
    'teacherEdit', 'teachingRecord', 'terms'
  ];
  const isValidUrl = (url:string) => {
    // 檢查是否包含非法字元
    if (/[\/\$?=]/.test(url)) {
      return false;
    }
    // 檢查是否包含非法字串
    if (forbiddenPaths.includes(url)) {
      return false;
    }
    return true;
  };
  if (!isValidUrl(brandWebIndex.value)) {
    ElMessage.error(i18n.t('edit.brandWebIndexError'));
    return;
  }
  // #endregion
  const brand: BrandEdit =
  {
    brandID: props.brandID,
    editingLanguage: brandLanguage.value,
    name: brandName.value,
    description: brandDescription.value,
    icon: icon.value,
    photo: photo.value,
    brandWebIndex: brandWebIndex.value,
    youtubeID: getYoutubeID(videoUrl.value),
  }
  console.log(brand);
  const status = await editBrand(brand);
  console.log(status)
  if (!status) {
    router.push(`/brandEdit/${brandWebIndex.value}?language=${brandLanguage.value}`)
      .then(() => {
        window.location.reload();//這裡要刷新一次頁面重callAPI 不然'我的品牌'按鈕會導到舊的路由
      });
  }
}

const getYoutubeID = (videoUrl: string) => {//把yt的ID從URL挖出來
  const regex = /(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = videoUrl.match(regex);
  return match ? match[1] : '';
}
</script>

<style scoped>
.brandEdit-form {
  padding-bottom: 10px;
}

.brandEdit-form :deep(.el-form-item__label) {
  padding-right: 0;
}

@media (max-width: 600px) {
  .responsive-form-item {
    display: block;
    /* 改變為 block 讓其換行 */
  }
}

.upload1-1 {
  display: flex;
  flex-wrap: wrap;
}

.upload16-9 {
  display: flex;
  flex-wrap: wrap;
}

.upload1-1 .el-upload {
  width: 100px;
  height: 100px;
}

.upload16-9 .el-upload {
  width: 178px;
  /* 16:9 aspect ratio */
  height: 100px;
  /* 16:9 aspect ratio */
}

.el-upload-list__item-thumbnail {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.el-icon-plus {
  font-size: 28px;
  color: #8c939d;
}

.iframe-container {
  position: relative;
  width: 100%;
  /* 可以根據需要調整父容器的最大寬度 */
  /* 16:9 比例 */
  padding-top: 56.25%;
  overflow: hidden;
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
</style>