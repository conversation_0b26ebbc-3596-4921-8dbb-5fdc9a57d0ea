{"language": "language", "menu": {"course": "Course", "course_ranking": "Course Ranking", "home": "Home", "logout": "Sign out", "message": "Message", "promote": "Promote", "teacher": "Teacher", "teaching_record": "Teaching Record", "account": "Account Info", "login": "<PERSON><PERSON>", "event": "Activity", "registed": "Already have an account", "notRegister": "Don't have an account yet", "register": "Sign up", "verify": "Verify", "toAPP": "APP Intro", "sendVerifyCode": "Send email verification letter", "code": "Please enter the verification code", "password": "password", "goLogin": "<PERSON><PERSON>", "goRegister": "Sign up", "goEmail": "The email has been sent to", "getCode": "Receive verification code", "emailNotReceive": "Reminder: The verification code letter may be in the spam folder. If you have not received it, please email us.", "emailRule": "Passwords must be a combination of at least six letters and numbers", "brand": "Brand", "brandEdit": "My Brand"}, "promote": {"already_sponsor": "You are already a sponsoring member", "award_sum": "Promotion bonus accumulation", "team_award_sum": "Accumulated team promotion bonus", "current_promotion_count": "The current number of your promotions has accumulated to {count} people", "current_team_promotion_count": "The current number of people promoted by your team has reached {count} people", "image_language": "Promote image language", "member_duration": "Membership period", "promote_description": "Promotion description", "promote_description_1": "When the user purchases to become a paid member, fill in your exclusive promotional discount code", "promote_description_2": "You can get {percentage}% cash back", "promote_description_3": "Users can get a gift card for {month} months for free", "promotion_count": "people", "your_promote_code": "Your exclusive promo code", "personalDetail": "Personal Promotion Record", "cusPhoto": "Customer Photo", "cusName": "Customer Name", "orderType": "Purchased <PERSON><PERSON>", "orderTime": "Purchase Time", "teamMemberName": "Team Member Name", "teamMemberPhoto": "Team Member Photo", "teamDetail": "Promotion Team Ranking", "allTime": "All Time", "pastYear": "Past Year", "pastMonth": "Past Month", "promoteTeam_description": "Promotion Team Explanation", "promoteTeam_description_1": "You are a promoting store owner (Level 2)", "promoteTeam_description_2": "As soon as your team members (Level 1) successfully promote users to become members and enter a promotion code", "promoteTeam_description_3": "1) Your team members (Level 1) can earn {percentage}% referral bonus", "promoteTeam_description_4": "2) You (Level 2) will also earn {percentage}% referral bonus", "promoteTeam_description_5": "3) Users can get a one-month gift card for free", "promoteTeam_description_6": "You are a mentor to the store owner (Level 3)", "promoteTeam_description_7": "You can earn the same generous bonus as the store owner", "promoteTeam_description_8": "You can become an introducer to the store owner (Level 2) through the 'Invite' button", "promoteTeam_description_9": "When the store's employee (Level 1) successfully promotes, you can earn {percentage}% of the sales amount as a coaching bonus", "promoteTeam_description_10": "1) The store's employee (Level 1) can earn {percentage}% referral bonus", "promoteTeam_description_11": "2) The store owner (Level 2) will also earn {percentage}% referral bonus", "promoteTeam_description_12": "3) You (Level 3) will also earn {percentage}% referral bonus", "promoteTeam_description_13": "4) Users can get a one-month gift card for free"}, "account": {"info": "Account Info", "delete_account": "Delete Account", "delete_account?": "Are you sure you want to delete your account?", "enter_userName": "Please enter your username", "delete_waring1": "After performing this action, iTalkuTalk will remove personal data, including email", "delete_waring2": "If you are absolutely sure you want to delete this account, please enter your username", "delete_cancel": "Cancel", "sure": "Confirm", "account_incorrect": "Incorrect username", "account_deleted": "Account Deleted", "terms": "privacy policy", "isMember": "You are already a member", "buyMember": "Buy membership", "buy1Month": "1 month", "buy6Months": "6 months", "buy12Months": "12 months", "easyTry": "Easy try", "mostPopular": "Popular", "average": "Average", "usd": "USD", "month": "month", "day": "days", "bestPrice": "Best Price", "iosterms": "TERMS OF USE"}, "student": "student", "event": {"address": "Address", "location": "Location", "introduction": "Introduction", "locationIntroduction": "Location Introduction", "member": "Member", "nonMember": "Non-member", "productPrice": "Original Price", "register": "", "registered": "E-ticket", "theme": "Topic", "earlyBirdDeadline": "Early bird", "earlyBirdPrice": "Early Bird Time", "eventFeatures": "\nActivity Features", "eventTime": "Time", "generalAdmission": "Regular Price", "useTicketInApp": "Use E-Ticket in the app", "shareFailed": "Co<PERSON> failed", "shareSuccess": "Copied to clipboard", "full": "Full", "weekdays": {"0": "Sunday", "1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday"}, "directRegister": "Buy at original price", "adsText": "Buy membership, get 200 TWD off", "before1": "Before ", "before2": "", "all": "All", "brand": "Brand", "teacher": "Teacher", "brandIntroduction": "Brand Intro", "teacherIntroduction": "Teacher <PERSON><PERSON>", "newEvent": "Current Activities", "oldEvent": "Past Activities", "RegInfoTitle": "Registration Info", "regInfo1": "Please confirm contact for registration info", "name": "Name", "regInfo2": "For any ticket issues, please contact customer service", "phone": "Number: +886 2 2771-2171 extension 2232", "yes": "Confirm", "no": "Cancel", "isTrans": "Recover", "dontTrans": "Show original", "ieRemoved": "Temporarily remove", "totalQuantity": "Capacity limit", "ticketType": "Ticket type", "attended": "Attended", "notAttended": "Absent", "ticketType0": "General ticket", "ticketType1": "Member ticket", "ticketType2": "Early bird general ticket", "ticketType3": "Early bird member ticket"}, "main": {"downloadApp": "Download the iTalkuTalk App now", "itutIntro1": "", "itutIntro2": "", "appSlogan": "iTalkuTalk:AI recognition", "slogan": "1 Million Downloads"}, "home": {"appDownloadA1": "iTalkuTalk AI language learning and cultural exchange community", "appDownloadA2": "Make it easy for you to learn", "appDownloadA3": "while communicating with friends around the world", "appDownloadB1": "APP downloads exceed 1 million", "appDownloadB2": "Users from over 200 countries", "appDownloadB3": "Ratings as high as 4.8 stars", "appGapA1": "AI language learning", "appGapA2": "Cultural exchange community", "appGapB1": "App function introduction", "appIntro1A1": "AI language recognition", "appIntro1B1": "English • Japanese • Korean • Chinese • Spanish • German • French • Russian • Vietnamese", "appIntro1B2": "Video learning in nine languages", "appIntro1B3": "Provides more than 10,000 learning videos", "appIntro1B4": "Single sentence loop playback", "appIntro1B5": "AI speaking practice", "appIntro2A1": "Games and study companions", "appIntro2B1": "Thousands of game videos", "appIntro2B2": "Online learning competition", "appIntro2B3": "Effective listening practice", "appIntro2B4": "International study partner matching", "appIntro2B5": "pronunciation practice listening and speaking", "appIntro3A1": "Special store buy one get one free", "appIntro3B1": "Game-based competitive learning: Thousands of online language learning games, designed to help you learn while competing with others.", "appIntro3B2": "", "appIntro3B3": "Regularly organize themed events", "appIntro3B4": "Oral communication, socializing and making friends", "appIntro3B5": "Members can enjoy another 40% off event", "appDownloadB0": "iOS and Android dual platforms", "appIntro6B1": "Immersive Learning for Parents with Toddlers and Preschoolers: Engage in immersive learning through bedtime stories, lullabies, children's songs, and language books.", "appIntro6B2": "Immersive Learning with Social Events: Enhance your learning experience by participating in themed social events.", "appIntro5B1": "Members who to go iTalkuTalk partners stores can get a buy-one-get-one-free offer on specific drinks", "appIntro5B2": "", "appIntro4B1": "Social learning: Make new friends from all over the world and practice learning with real people through audio and video calls", "appIntro4B2": "", "appDownloadA2B": "English • Japanese • Korean • Chinese • Spanish • German • French • Russian • Vietnamese", "appIntro7B1": "Learn new vocabulary through 10 different videos from native speakers to improve overall understanding.", "appIntro7B2": "", "appIntro6B3": "Immersive Learning with Popular Music: Learn through the integration of popular music.", "appIntro8B1": "Loving sponsor member", "appIntro8B2": "APP full function", "appIntro8B3": "Drinks buy one get one free", "appIntro8B4": "Event ticket discounts", "trademark1": "iTalkuTalk Trademarks", "trademark2": "All are registered trademarks", "patentA1": "Patented technologies invented in the United States and Taiwan", "patentB1": "Patent number", "patentB2": "Patent title", "companyInformation": "Company information", "contactInformation": "Customer Service", "email": "Contact Email: italkutalktw{'@'}gmail.com", "phone": "Contact Number: +886 2 2771-2171 extension 2232", "title": "iTalkuTalk: AI recognition", "address": "Contact Address：Rm.406, No.1-Integrated Technology Complex, Sec.3, Zhongxiao E.Rd., Da'an Dist.,Taipei City,Taiwan (R.O.C.)", "appIntro6B4": "Immersive Learning with Comics: Enjoy immersive learning with comics.", "appIntro6B5": "Immersive Learning with Cartoons: Dive into immersive learning with cartoons."}, "terms": {"header": "TERMS OF USE", "privacy": "iTalkuTalk privacy statement", "privacy1": "Thank you for using the iTalkuTalk application programming interface (hereinafter referred to as iTalkuTalk). Using iTalkuTalk means that you agree to the following terms. If you do not agree to any of these terms, you will not be authorized to use it. We reserve the right to update and change these terms at any time without notice. You are encouraged to review this statement regularly to learn about the latest information about the privacy statement.", "privacy2": "Your iTalkuTalk authorization will continue to be bound by these terms until either party terminates the terms. You can stop using all or any of them and terminate the authorization with iTalkuTalk. iTalkuTalk may terminate authorization at any time for any reason. If the following circumstances occur, your iTalkuTalk usage right will be automatically terminated:", "privacyDetailA1": "1. Violation of any terms.", "privacyDetailA2": "2. iTalkuTalk publicly publishes a written notice of termination.", "privacyDetailA3": "3. iTalkuTalk will send you a written notice of termination.", "privacyDetailA4": "4. iTalkuTalk disables your iTalkuTalk access right.", "privacyDetailB1": "1. Scope of application: This privacy statement applies to users who have registered iTalkuTalk.", "privacyDetailB2": "2. Effective date: from the date the user registers iTalkuTalk.", "privacyDetailB3": "3. Information collection: When you use iTalkuTalk apps, websites, and other online products and services (collectively referred to as services), iTalkuTalk will collect information about you:", "privacyDetailB31": "(1) Information you provide us directly, may include but not limited to name, email address, gender, region , and native language, etc.", "privacyDetailB32": "(2) When you use iTalkuTalk services, the information collected through your use of iTalkuTalk.", "privacyDetailB33": "(3) Information sharing iTalkuTalk may share information collected about you, including information you provide to us or information collected through your use of iTalkuTalk.", "privacyDetailB34": "(4) To request correction or you can ask us to delete or delete your personal data: Users can go to the information settings on the personal page and click Delete account. After receiving the request, iTalkuTalk will comply with personal data access, correction or deletion requirements in accordance with the provisions of the Personal Data Protection Law. Deleting the account will delete all personal emails, photos, video viewing records, chat room records, and learning history,etc.", "privacyDetailB35": "(5) Data collection purpose: iTalkuTalk will build a customized learning plan by the information you provide to us or information collected through your use of iTalkuTalk.", "privacyDetailB4": "4. <PERSON><PERSON> of responsibility", "privacyDetailB41": "(1) In any case, even if iTalkuTalk is aware of the possibility of such damage, whether it is based on breach of contract, breach of warranty, infringement (including negligence, product liability or other circumstances), or any other money For losses, iTalkuTalk shall not be liable for compensation for any indirect, incidental, derivative, special or punitive damages caused or related to your use of iTalkuTalk. In any case, iTalkuTalk is not responsible to you.", "privacyDetailB5": "5. Contact information: If you have any questions about this statement, please email us: italkutalktw{'@'}gmail.com", "UGC": "iTalkuTalk User-generated content(UGC)", "UGCDetail": "This program provides users with the ability to post and privately transmit information, data, text, images, information or other types of data (hereinafter referred to as \"User Content\"). The media content of this program shall be borne by the provider of the user content All related are responsible. iTalkuTalk cannot control the user content posted through this platform, and will not be able to ensure the appropriateness, correctness and completeness of the user content. Appropriate, unpleasant user content (for example: defamatory, insulting, threatening, obscene, obscene, intrusive, untruthful, or contrary to morals and public order text, images, or any type of media). Therefore, under no circumstances shall iTalkuTalk be responsible for any User Content and any resulting from posting, e-mailing or transmission via this Platform, including but not limited to any errors or omissions. For the user content appearing in this program, if there is any infringement on any individual, unit, organization or company, please notify iTalkuTalk by e-mail (italkutalktw{'@'}gmail.com), iTalkuTalk will remove it unconditionally, but not the provider bear any legal responsibility (for example: intellectual property rights such as copyrights, trademark rights, patent rights, reputation rights, privacy rights)."}, "brand": {"score": "Rating", "eventCount": "Activity Count", "seeScore": "View Reviews", "detail": "Introduction", "doTrans": "Translate", "teacherRating": "Teacher", "eventRating": "Activity", "envRating": "Environment", "commentText": "Review", "edit": "Edit", "addTeacher": "Add teacher", "addEvent": "Add activity", "addPastEvent": "Add past activity", "deleteTeacher": "Delete teacher", "deleteTeacherText1": "Are you sure you want to delete the teacher data for ", "deleteTeacherText2": " ?", "editTeacher": "Edit teacher", "getRegistrationList": "Registration status", "downEven": "Unpublish activity", "upEvent": "Publish", "deleteEvent": "Delete", "downEventText1": "Unpublish ", "downEventText2": "?", "upEventText1": "Publish ", "upEventText2": "?", "deleteEventText1": "Delete all data of ", "deleteEventText2": "?"}, "teacher": {"nationality": "Nationality", "teacher": "Teacher", "language": "Language", "score": "Rating", "eventCount": "Courses", "brand": "Brand"}, "nationality": {"TW": "Taiwan", "CN": "China", "HK": "Hong Kong", "MO": "Macau", "US": "United States", "CA": "Canada", "MX": "Mexico", "GB": "United Kingdom", "FR": "France", "CZ": "Czech Republic", "DE": "Germany", "NZ": "New Zealand", "FI": "Finland", "NL": "Netherlands", "NO": "Norway", "CH": "Switzerland", "PT": "Portugal", "ES": "Spain", "TR": "Turkey", "BE": "Belgium", "CL": "Chile", "MM": "Myanmar", "MN": "Mongolia", "JO": "Jordan", "JP": "Japan", "KP": "North Korea", "KR": "South Korea", "SG": "Singapore", "TH": "Thailand", "PH": "Philippines", "VN": "Vietnam", "KW": "Kuwait", "PK": "Pakistan", "PL": "Poland", "KH": "Cambodia", "KE": "Kenya", "ET": "Ethiopia", "LR": "Liberia", "NP": "Nepal", "RO": "Romania", "RU": "Russia", "UA": "Ukraine", "UG": "Uganda", "SY": "Syria", "RW": "Rwanda", "SA": "Saudi Arabia", "SB": "Solomon Islands", "SC": "Seychelles", "SD": "Sudan", "SE": "Sweden", "CO": "Colombia", "CR": "Costa Rica", "HU": "Hungary", "ID": "Indonesia", "IE": "Ireland", "IL": "Israel", "IN": "India", "CU": "Cuba", "DK": "Denmark", "EG": "Egypt", "GR": "Greece", "LY": "Libya", "MA": "Morocco", "MC": "Monaco", "MD": "Moldova", "MG": "Madagascar", "MH": "Marshall Islands", "HN": "Honduras", "AF": "Afghanistan", "AR": "Argentina", "AT": "Austria", "AU": "Australia", "BR": "Brazil", "IQ": "Iraq", "IR": "Iran", "IS": "Iceland", "PA": "Panama", "PE": "Peru", "IT": "Italy", "BS": "Bahamas", "GM": "Gambia", "KZ": "Kazakhstan", "LA": "Laos", "LB": "Lebanon", "LC": "Saint Lucia", "GN": "Guinea", "AG": "Antigua and Barbuda", "AI": "<PERSON><PERSON><PERSON>", "AL": "Albania", "AM": "Armenia", "AO": "Angola", "AQ": "Antarctica", "AS": "American Samoa", "AW": "Aruba", "AZ": "Azerbaijan", "BI": "Burundi", "BB": "Barbados", "BD": "Bangladesh", "BF": "Burkina Faso", "BG": "Bulgaria", "BH": "Bahrain", "BJ": "Benin", "BM": "Bermuda", "BN": "Brunei", "BO": "Bolivia", "BT": "Bhutan", "BV": "Bouvet Island", "BW": "Botswana", "BY": "Belarus", "BZ": "Belize", "CC": "Cocos (Keeling) Islands", "CD": "Congo (Kinshasa)", "CF": "Central African Republic", "CI": "Ivory Coast", "CM": "Cameroon", "CP": "Clipperton Island", "CY": "Cyprus", "DM": "Dominica", "DO": "Dominican Republic", "DZ": "Algeria", "EC": "Ecuador", "EE": "Estonia", "EH": "Western Sahara", "ER": "Eritrea", "FJ": "Fiji", "FK": "Falkland Islands", "FO": "Faroe Islands", "GA": "Gabon", "GD": "Grenada", "GF": "French Guiana", "GH": "Ghana", "GI": "Gibraltar", "GL": "Greenland", "GU": "Guam", "GQ": "Equatorial Guinea", "GT": "Guatemala", "GW": "Guinea-Bissau", "GY": "Guyana", "HM": "Heard Island and McDonald Islands", "HR": "Croatia", "HT": "Haiti", "IO": "British Indian Ocean Territory", "JM": "Jamaica", "KG": "Kyrgyzstan", "KI": "Kiribati", "KM": "Comoros", "KN": "Saint Kitts and Nevis", "KY": "Cayman Islands", "LI": "Liechtenstein", "LK": "Sri Lanka", "LS": "Lesotho", "LT": "Lithuania", "LU": "Luxembourg", "LV": "Latvia", "MK": "North Macedonia", "ML": "Mali", "MR": "Mauritania", "MT": "Malta", "MU": "Mauritius", "MV": "Maldives", "MW": "Malawi", "MY": "Malaysia", "NA": "Namibia", "NC": "New Caledonia", "NE": "Niger", "NF": "Norfolk Island", "NG": "Nigeria", "NI": "Nicaragua", "NR": "Nauru", "NU": "Niue", "PF": "French Polynesia", "PN": "Pitcairn Islands", "PC": "Pacific Islands Trust Territory", "PR": "Puerto Rico", "PW": "<PERSON><PERSON>", "PY": "Paraguay", "QA": "Qatar", "RE": "Réunion", "SH": "Saint Helena", "SI": "Slovenia", "SJ": "Svalbard and <PERSON>", "SK": "Slovakia", "SL": "Sierra Leone", "SM": "San Marino", "SN": "Senegal", "SO": "Somalia", "SR": "Suriname", "SV": "El Salvador", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "TC": "Turks and Caicos Islands", "TD": "Chad", "TG": "Togo", "TJ": "Tajikistan", "TK": "Tokelau", "TM": "Turkmenistan", "TN": "Tunisia", "TO": "Tonga", "TT": "Trinidad and Tobago", "TV": "Tuvalu", "TZ": "Tanzania", "UM": "United States Minor Outlying Islands", "UY": "Uruguay", "UZ": "Uzbekistan", "VA": "Vatican City", "VC": "Saint Vincent and the Grenadines", "VE": "Venezuela", "VG": "British Virgin Islands", "VI": "United States Virgin Islands", "VU": "Vanuatu", "WF": "Wallis and Futuna", "YE": "Yemen", "YT": "Mayotte", "ZA": "South Africa", "ZM": "Zambia", "ZW": "Zimbabwe"}, "Language": {"zh": "Chinese", "en": "English", "ko": "Korean", "ja": "Japanese", "es": "Spanish", "ru": "Russian", "de": "German", "fr": "French", "vi": "Vietnamese"}, "edit": {"save": "Save", "brandLanguage": "Display text", "brandName": "Brand Name", "inputBrandName": "Please enter name", "brandDescription": "Description", "inputBrandDescription": "Please enter description", "brandIcon": "Trademark", "brandPhoto": "Cover and Background", "brandWebIndex": "URL", "inputBrandWebIndex": "Use as URL", "brandVideoUrl": "Introduction video", "inputBrandVideoUrl": "Enter YouTube link", "newBrand": "Add new brand", "cannotBeEmpty": "Required fields cannot be empty", "uploadSuccess": "Upload successful", "uploadError": "Upload error", "reUpload": "Reupload", "typeError": "Image format error", "tooBig": "Image compression in progress, please wait.", "editBrand": "Edit brand", "editLanguage": "Edit language", "teacherName": "Teacher name", "inputTeacherName": "Please enter the teacher's name", "teacherDescription": "Description", "inputTeacherDescription": "Please enter description", "teacherIcon": "Profile picture", "teacherPhoto": "Cover photo", "teacherWebIndex": "URL", "teacherVideoUrl": "Introduction video", "webIndexError": "The URL already exists, please choose another URL", "eventLanguage": "Language", "eventPhoto": "Activity photo", "inputEventName": "Please enter theme", "inputEventDescription": "Please enter activity description", "eventPlace": "Place", "locationSearch": "Search location", "useLocationSearch": "Insert data below after search", "storeName": "Store name", "storeAddress": "Store address", "eventTeacher": "Activity teacher", "noTeacher": "None", "eventType": "Activity type", "earlyPrice": "Early bird price", "totalQuantity": "Participants", "meals": "Meal included", "inputEventFeature": "Enter activity features (optional)", "inputMeals": "Enter included meal (optional)", "locationIntroduction": "Enter location description (optional)", "over500": "The ticket price is at least 600 TWD", "timeError": "Activity time set incorrectly", "eventPhotoDescription": "Upload past event photos to attract users to register.", "primaryLanguageDescription": "Enter your native language, the system will automatically translate into nine languages.", "editingLanguageDescription": "If you are not satisfied with the system's translation, you can directly add the information in that language.", "brandWebIndexError": "The URL is already in use or contains illegal characters."}, "iosterms": {"header": "TERMS OF USE", "privacy": "Please read below about the auto-renewing subscription nature of these products", "privacyDetailA1": "-Payment will be charged to iTunes Account at confirmation of purchase", "privacyDetailA2": "-Subscription automatically renews unless auto-renew is turned off at least 24-hours before the end of the current period", "privacyDetailA3": "-Account will be charged for renewal within 24-hours prior to the end of the current period, and identify the cost of the renewal", "privacyDetailA4": "-Subscriptions may be managed by the user and auto-renewal may be turned off by going to the user’s Account Settings after purchase"}}