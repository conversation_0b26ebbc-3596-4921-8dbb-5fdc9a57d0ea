<script setup lang="ts">
const props = defineProps({
  months: Number,
  count: Number,
});
</script>
<template>
  <el-space style="width: 100%" :size="15">
    <slot />
    <div>
      <el-space style="align-items: start" direction="vertical" :size="0">
        <div>
          <text class="statistic-count">{{ props.count }}</text>
          <text class="statistic-description">
            {{ " " + $t("promote.promotion_count") }}</text
          >
        </div>
        <div>
          <text class="statistic-month">{{ props.months }} Month</text>
        </div>
      </el-space>
    </div>
  </el-space>
</template>
