<script lang="ts" setup>
import { TeamDetail } from "~~/models/promote";
const props = defineProps({
  detail: { type: Object as () => TeamDetail[], default: null },
});
</script>

<template>
  <el-table :data="detail" style="width: 100%">
    <el-table-column :label="$t('promote.teamMemberPhoto')" width="100">
      <template #default="{ row }">
        <img :src="row.teamMemberPhoto" :alt="$t('promote.teamMemberPhoto')"
          style="width: 70px; height: 70px; object-fit: cover;" />
      </template>
    </el-table-column>
    <el-table-column prop="teamMemberName" :label="$t('promote.teamMemberName')" sortable width="110" />
    <el-table-column prop="teamMemberTotal.oneMonth" label="1 Month" sortable width="110" />
    <el-table-column prop="teamMemberTotal.sixMonths" label="6 Month" sortable width="110" />
    <el-table-column prop="teamMemberTotal.oneYear" label="12 Month" sortable width="120" />
  </el-table>
</template>

