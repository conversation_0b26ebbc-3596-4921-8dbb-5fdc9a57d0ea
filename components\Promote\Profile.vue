<script setup lang="ts">
import { MemberInfo } from "~~/models/promote";
const props = defineProps({
  userPhoto: {
    type: String,
    default: "https://itutbox.s3.amazonaws.com/pic/default_personal_2019.jpg",
  },
  memberInfo: {
    type: Object as () => MemberInfo,
    default: null,
  },
});
</script>
<template>
  <el-card shadow="always">
    <el-skeleton v-if="memberInfo == null" :rows="2" animated />
    <el-space v-else style="align-items: start" :size="16">
      <el-avatar :size="72" :src="props.userPhoto"></el-avatar>
      <el-space
        :size="16"
        :fill="true"
        direction="vertical"
        :style="{ 'align-items': 'start' }"
      >
        <div>{{ $t("promote.already_sponsor") }} (Level {{memberInfo.level}})</div>
        <div class="duration-bg">
          <text class="duration-text">
            {{ $t("promote.member_duration") }} :
            {{ new Date(memberInfo?.startTime).toLocaleDateString() }} ~
            {{ new Date(memberInfo?.endTime).toLocaleDateString() }}
          </text>
        </div>
      </el-space>
    </el-space>
  </el-card>
</template>
