<template>
  <div class="main-div">
    <div class="">
      <div class="teacherImage-wrapper">
        <img :src=teacherDetail?.brandPhoto class="teacherImage" alt="品牌照片" />
      </div>
      <div class="content-wrapper">
        <div class="introMenu-content">
          <div class="teacherIntro-content">
            <el-avatar :style="{ minWidth: '72px' }" :size="72" :src=teacherDetail?.icon></el-avatar>
            <div class="teacherIntro-text">
              <div><div class="teacherName">{{ teacherDetail?.teacherName }}</div></div>
              <div class="teacherScore">
                <span class="">{{ $t('teacher.score') }}：{{ teacherDetail?.rating }}</span>
                <EventViewRating :teacherID="teacherDetail?.teacherID"></EventViewRating>
              </div>
              <div><span class="">{{ $t('teacher.eventCount') }}：{{ teacherDetail?.eventCount }}</span></div>
              <div><span class="">{{ $t('teacher.nationality') }}：{{ $t(`nationality.${teacherDetail?.nationality}`)
                  }}/{{
          $t(`Language.${teacherDetail?.primaryLanguage}`) }}</span></div>
              <div><span class="">{{ $t('teacher.brand') }}：{{ teacherDetail?.brandName }}</span></div>
            </div>
          </div>
          <EditTeacherMenu v-if="isEdit && teacherDetail" :teacherID="teacherDetail.teacherID" :isInside=true>
          </EditTeacherMenu>
        </div>
        <div class="teacherDetail-content">
          <div class="detailTitle">
            <span>{{ $t('brand.detail') }}</span>
            <EventTrans v-if="teacherDetail?.oriLanguage != language" @update:isTrans="isTrans = $event"
              :text="'brand.detail'"></EventTrans>
          </div>
          <div v-if="isTrans" class="formatted-description trans-description" v-html="transDescriptionHtml"></div>
          <div v-if="!isTrans" class="formatted-description ori-description" v-html="oriDescriptionHtml"></div>
        </div>
        <div v-if="teacherDetail?.youtubeID" class="iframe-container">
          <iframe :src="'https://www.youtube.com/embed/' + teacherDetail.youtubeID" title="YouTube video player"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
        </div>
      </div>
      <TeacherTabs @update:selectTag="selectTag = $event"></TeacherTabs>
      <div class="padding-div">
        <el-col v-if="teacherDetail" :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-space :fill="true" style="width: 100%">
            <div v-if="selectTag === '0'">
              <EventCard :teacherID=teacherDetail?.teacherID :is-past=false />
            </div>
            <div v-if="selectTag === '1'">
              <EventCard :teacherID=teacherDetail?.teacherID :is-past=true />
            </div>
          </el-space>
        </el-col>
      </div>
    </div>
    <div class="right-div"></div>
  </div>

</template>

<script setup lang="ts">
import { ref, watch, Directive } from 'vue';
import { getTeacherDetail } from "../../api/teacher";
import { getGuestKey } from "../../api/user";
import { TeacherDetail } from "~~/models/teacher";
import { User } from "~~/models/user";
import { globalStore } from "../../stores/global";
import { useI18n } from "vue-i18n";
const { t } = useI18n()
const route = useRoute();
const store = globalStore();
const loading = ref(true);
const isTrans = ref(true);
const teacherDetail = ref<TeacherDetail>();
let user: User;
const language = ref(route.query.language || 'default');
const selectTag = ref('0');
const props = defineProps({
  teacherID: {//這邊傳進來有可能是ID 有可能其實是WebIndex 要注意
    type: String,
    required: true,
  },
  isEdit: {
    type: Boolean,
    required: true,
  }
});
onMounted(async () => {//進入畫面時
  await getDetail();//取得教師詳情
  loading.value = false;
  watchEffect(() => {//寫在這裡而非index.vue 或是上面一點 是因為要等teacherDetail有值之後才能call
    useHead({
      title: `${teacherDetail.value?.teacherName ?? ''} - ${t('home.title')}`,
    });
  });
});
async function getDetail() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    teacherDetail.value = await getTeacherDetail({ guestKey: guestKey, language: languageString, teacherID: props.teacherID });
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    teacherDetail.value = await getTeacherDetail({ teacherID: props.teacherID });
  }
}
function handleDoNotThing(event: MouseEvent) {//點擊卡片時
  event.stopPropagation();
}
watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  loading.value = true; // 設置加載狀態
  getDetail(); // 重新獲取教師詳情
  loading.value = false; // 加載完成
});

//#region 簡介中可以顯示出超連結
const transDescriptionHtml = ref('');
const oriDescriptionHtml = ref('');
const linkify = (text: string) => {
  const urlRegex = /https?:\/\/[^\s]+/g;
  return text.replace(urlRegex, (url) => {
    return `<a href="${url}" target="_blank" style="color: blue;">${url}</a>`;
  });
};
const updateDescriptions = () => {
  if (teacherDetail.value) {
    if (teacherDetail.value.transDescription) {
      transDescriptionHtml.value = linkify(teacherDetail.value.transDescription);
    }
    if (teacherDetail.value.oriDescription) {
      oriDescriptionHtml.value = linkify(teacherDetail.value.oriDescription);
    }
  }
};
onMounted(() => {
  updateDescriptions();
});
watch(//切換語言時
  () => teacherDetail.value?.transDescription,
  () => {
    updateDescriptions();
  }
);
watch(//切換語言時
  () => teacherDetail.value?.oriDescription,
  () => {
    updateDescriptions();
  }
);
//#endregion
</script>

<style scoped>
.main-div {
  display: flex;
}

.right-div {
  min-width: 250px;
  background-color: #FFFFFF
}

.teacherImage-wrapper {
  width: 100%;
  max-height: 20vh;
  /* 設置圖片的寬高比 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.teacherImage {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}

.content-wrapper {
  padding: 15px 25px 0px 25px;
  background-color: #FFFFFF;
  overflow-x: scroll;
}

.content-wrapper::-webkit-scrollbar {
  display: none;
}

@media (max-width: 500px) {
  .content-wrapper {
    padding: 10px 10px 0px 10px;
  }
}

.introMenu-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.teacherIntro-content {
  display: flex;
  align-items: center;
}

.teacherName {
  font-size: 24px;
}

.teacherIntro-text {
  margin-left: 10px;
  white-space: nowrap;
  /* 防止文字換行 */
}

.teacherScore {
  display: flex;
  align-items: center;
  gap: 10px;
}

.teacherDetail-content {
  padding: 15px 0px 10px 0px;
}

.detailTitle {
  font-size: 20px;
  margin-bottom: 10px;
  align-items: center;
  display: flex;
  gap: 10px;
}

.formatted-description {
  white-space: pre-wrap;
}

.iframe-container {
  position: relative;
  width: 100%;
  /* 可以根據需要調整父容器的最大寬度 */

  /* 16:9 比例 */
  padding-top: 56.25%;
  overflow: hidden;
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.padding-div {
  padding: 20px;
  overflow-x: auto;
  /* 允許橫向滑動 */
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  /* 平滑滾動，適用於移動設備 */
}

.padding-div::-webkit-scrollbar {
  display: none;
  /* 隱藏滾動條 */
}

@media (max-width: 500px) {
  .padding-div {
    padding: 0px;
  }
}

@media (max-width: 1300px) {
  .right-div {
    min-width: 0px;
  }

}

@media (min-width: 1300px) {
  .detailTitle {
    font-size: 24px;
  }

  .formatted-description {
    font-size: 18px;
  }

  /*.teacherIntro-text span {
    font-size: 18px;
  }*/

}
</style>