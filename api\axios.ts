import { ElMessage } from "element-plus";
import StatusCode from "./statusCode";
import axios from "axios";
import { globalStore } from "~~/stores/global";

const runtimeConfig = useRuntimeConfig();

const instance = axios.create({
  baseURL: runtimeConfig.public.axiosBaseUrl,
  timeout: 5000, // 請求超時時間
});

instance.interceptors.request.use(function (request) {
  let store = globalStore();
  let user = store.getCurrentUser;
  let auth = {};
  if (user != null) auth = { userID: user.userID, accessKey: user.accessKey };

  // 大多post方法都要將userID, accessKey加入request body作為驗證內容
  // 之後可能會使用jwt或session作為驗證方式
  if (request.method == "post" && request.url != "/api/user/signin")
    request.data = { ...request.data, ...auth };

  return request;
});
// Add a response interceptor
instance.interceptors.response.use(
  function (response) {
    let store = globalStore();
    if (response.data.status !== 0) {
      switch (response.data.status) {
        case StatusCode.AccessKeyIllegal:
          // accessKey過期，讓使用者重新登入
          store.clearUserCache();
          setTimeout(() => window.location.reload(), 3000);
      }
      // TODO: 這裡加上錯誤的多國語訊息
      ElMessage.error(
        `Error: ${response.data.status}\n${response.data.errMsgs}`
      );
      throw new Error(response.data.status);
    }
    return response;
  },
  function (error) {
    ElMessage.error(`Error: ${error.response.data}`);
    return Promise.reject(error);
  }
);

export default instance;
