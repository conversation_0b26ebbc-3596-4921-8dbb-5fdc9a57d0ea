<script lang="ts" setup>
definePageMeta({
  middleware: ["unauth"],
});
</script>
<template>
  <div class="padding-div">
    <el-col :sm="24" :md="16">
      <!--第一框顯示帳號資料-->
      <el-space :fill="true" style="width: 100%">
        <div class="iosterms-info">
          <div class="iosterms-header">
            <h2>{{ $t('iosterms.header') }}</h2>
          </div>

          <div class="iosterms-content">
            <div class="iosterms-title" style="color: black;">
              <h3>{{ $t('iosterms.privacy') }}</h3>
            </div>
            <div class="iosterms-details">
              <p>{{ $t('iosterms.privacyDetailA1') }}</p>
              <p>{{ $t('iosterms.privacyDetailA2') }}</p>
              <p>{{ $t('iosterms.privacyDetailA3') }}</p>
              <p>{{ $t('iosterms.privacyDetailA4') }}</p>
            </div>
          </div>
        </div>
      </el-space>
    </el-col>
  </div>
</template>
<style scoped>
.iosterms-info {
  padding: 24px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.iosterms-header {
  margin-bottom: 10px;
}

.iosterms-content {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 15px;
  color: #444444;
  margin-bottom: 30px;
}

.padding-div {
  padding: 20px;
}
</style>