<template>
  <div class="teacherEdit-form">
    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.brandLanguage') }}：
      </template>
      <el-select v-model="editingLanguage" placeholder="Select" style="width: 140px">
        <el-option v-for="lang in supportLangs" :key="lang" :label="$t(`Language.${lang}`)" :value="lang" />
      </el-select>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.teacherName') }}：
      </template>
      <el-input v-model="teacherName" :placeholder="$t('edit.inputTeacherName')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.teacherDescription') }}：
      </template>
      <el-input v-model="teacherDescription" type="textarea" :rows="4"
        :placeholder="$t('edit.inputTeacherDescription')"></el-input>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('teacher.nationality') }}：
      </template>
      <el-select filterable v-model="nationality" placeholder="Select" style="width: 140px">
        <el-option v-for="country in countryCodes" :key="country" :label="$t(`nationality.${country}`)"
          :value="country" />
      </el-select>
    </el-form-item>

    <el-form-item>
      <template #label>
        <span style="color: red">*</span> {{ $t('teacher.language') }}：
      </template>
      <el-select v-model="primaryLanguage" placeholder="Select" style="width: 140px">
        <el-option v-for="lang in supportLangs" :key="lang" :label="$t(`Language.${lang}`)" :value="lang" />
      </el-select>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.teacherIcon') }}(1:1)：
      </template>
      <EditUploadImage v-if="doneLoading" :oldImageUrl="teacherDetail.icon" v-model:imageUrl="icon" :type="1" :frontPath="'brand'" :backPath="'teacher/icon'"></EditUploadImage>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.teacherPhoto') }}(16:9)：
      </template>
      <EditUploadImage v-if="doneLoading" :oldImageUrl="teacherDetail.teacherPhoto" v-model:imageUrl="photo" :type="2" :frontPath="'brand'" :backPath="'teacher/photo'"></EditUploadImage>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        <span style="color: red">*</span> {{ $t('edit.teacherWebIndex') }}：https://www.italkutalk.com/
      </template>
      <el-input v-model="teacherWebIndex" :placeholder="$t('edit.inputBrandWebIndex')"></el-input>
    </el-form-item>

    <el-form-item class="responsive-form-item">
      <template #label>
        {{ $t('edit.teacherVideoUrl') }}：
      </template>
      <el-input v-model="videoUrl" :placeholder="$t('edit.inputBrandVideoUrl')"></el-input>
    </el-form-item>
    <div v-if="videoUrl" class="iframe-container">
      <iframe :src="'https://www.youtube.com/embed/' + getYoutubeID(videoUrl)" title="YouTube video player"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
    </div>
  </div>
  <el-button type="primary" size="large" @click="save">{{ $t('edit.save') }}</el-button>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { supportLangs, countryCodes } from "~~/util/language";
import { TeacherEdit, TeacherDetail } from "../../models/teacher"
import { editTeacher, addTeacher, getTeacherDetail } from "../../api/teacher"
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';
import cloneDeep from 'lodash/cloneDeep';
const props = defineProps({
  brandID: {
    type: String,//所屬品牌ID
    default: ''
  },
  teacherID: {
    type: String,
    default: ''
  },
  isInside: {
    type: Boolean,//在教師主頁內編輯 需要跳轉
    default: false
  },
  isAdd: {
    type: Boolean,//新增教師 
    default: false
  }
});
const i18n = useI18n();
const editingLanguage = ref('');
const teacherName = ref('');
const teacherDescription = ref('');
const icon = ref('');
const photo = ref('');
const nationality = ref('');
const primaryLanguage = ref('');
const teacherWebIndex = ref('');
const videoUrl = ref('');
const router = useRouter();
const doneLoading = ref(false);//初始化完成 避免舊圖片顯示不出來

const initialTeacherDetail = {//初始值
  teacherID: '',
  brandName: '',
  teacherPhoto: '',
  brandPhoto: '',
  youtubeID: '',
  icon: '',
  teacherName: '',
  rating: 0,
  teacherWebIndex: '',
  nationality: '',
  primaryLanguage: '',
  transDescription: '',
  oriDescription: '',
  oriLanguage: '',
  eventCount: 0,
};
const teacherDetail = ref<TeacherDetail>(cloneDeep(initialTeacherDetail));

onMounted(async () => {//初始化 如果teacgerDetail有值(編輯時) 帶入資料
  if (!props.isAdd)//如果不是新增教師，就需要先抓一次資料
  {
    teacherDetail.value = await getTeacherDetail({ teacherID: props.teacherID });
    editingLanguage.value = teacherDetail.value.oriLanguage;
    teacherName.value = teacherDetail.value.teacherName;
    teacherDescription.value = teacherDetail.value.oriDescription;
    icon.value = teacherDetail.value.icon;
    photo.value = teacherDetail.value.teacherPhoto;
    teacherWebIndex.value = teacherDetail.value.teacherWebIndex;
    nationality.value = teacherDetail.value.nationality;
    primaryLanguage.value = teacherDetail.value.primaryLanguage;
    videoUrl.value = teacherDetail.value.youtubeID
      ? `https://www.youtube.com/watch?v=${teacherDetail.value.youtubeID}`
      : '';
  }
  doneLoading.value = true;
});

watch(() => editingLanguage.value, async (newLang) => {//切換編輯語言時會重抓資料
  if (newLang != teacherDetail.value.oriLanguage)//如果跟之前的不一樣 清空名字與描述 避免語言錯誤
  {
    teacherName.value = '';
    teacherDescription.value = '';
  } else {//如果一樣 就再帶入上次資料
    teacherName.value = teacherDetail.value.teacherName || '';
    teacherDescription.value = teacherDetail.value.oriDescription || '';
  }
});

const save = async () => {//儲存 會檢查然後上傳、重導向
  if (!editingLanguage.value || !teacherName.value || !teacherDescription.value ||
    !icon.value || !photo.value || !nationality.value || !primaryLanguage.value || !teacherWebIndex.value || teacherWebIndex.value.length >= 24) {
    ElMessage.error(i18n.t('edit.cannotBeEmpty'));
    return;
  }
  const teacher: TeacherEdit =
  {
    brandID: props.brandID,
    teacherID: props.teacherID,
    editingLanguage: editingLanguage.value,
    name: teacherName.value,
    description: teacherDescription.value,
    icon: icon.value,
    photo: photo.value,
    nationality: nationality.value,
    primaryLanguage: primaryLanguage.value,
    teacherWebIndex: teacherWebIndex.value,
    youtubeID: getYoutubeID(videoUrl.value),
  }
  console.log(teacher);
  let status = 0;
  if (props.isAdd) {
    status = await addTeacher(teacher);//新增教師
  } else {
    status = await editTeacher(teacher);//編輯教師
    if (props.isInside && !status) {//如果在頁面內編輯 要跳轉
      router.push(`/teacherEdit/${teacherWebIndex.value}?language=${editingLanguage.value}`)
    }
  }
  window.location.reload();//這裡要刷新一次頁面重callAPI 避免奇妙bug
}

const getYoutubeID = (videoUrl: string) => {//把yt的ID從URL挖出來
  const regex = /(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  const match = videoUrl.match(regex);
  return match ? match[1] : '';
}
</script>

<style scoped>
.teacherEdit-form{
  padding-bottom: 10px;
}

.teacherEdit-form :deep(.el-form-item__label) {
  padding-right: 0;
}

@media (max-width: 600px) {
  .responsive-form-item {
    display: block;
    /* 改變為 block 讓其換行 */
  }
}

.upload1-1 {
  display: flex;
  flex-wrap: wrap;
}

.upload16-9 {
  display: flex;
  flex-wrap: wrap;
}

.upload1-1 .el-upload {
  width: 100px;
  height: 100px;
}

.upload16-9 .el-upload {
  width: 178px;
  /* 16:9 aspect ratio */
  height: 100px;
  /* 16:9 aspect ratio */
}

.el-upload-list__item-thumbnail {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.el-icon-plus {
  font-size: 28px;
  color: #8c939d;
}

.iframe-container {
  position: relative;
  width: 100%;
  /* 可以根據需要調整父容器的最大寬度 */
  /* 16:9 比例 */
  padding-top: 56.25%;
  overflow: hidden;
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
</style>