import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { CognitoIdentityClient } from '@aws-sdk/client-cognito-identity';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-provider-cognito-identity';

let s3: S3Client | null = null;

if (process.client) {
  const AWS_REGION = "ap-northeast-1";
  const IDENTITY_POOL_ID = "ap-northeast-1:df6921bb-4dab-4032-a45a-fca0d7eac9c7";

  s3 = new S3Client({
    region: AWS_REGION,
    credentials: fromCognitoIdentityPool({
      client: new CognitoIdentityClient({ region: AWS_REGION }),
      identityPoolId: IDENTITY_POOL_ID,
    }),
  });
}

export default s3;