<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { globalStore } from "../stores/global";

let store = globalStore();
const i18n = useI18n();

// Warning: i18n套件的當下語言只能在vue component中設定
onMounted(() => {
  i18n.locale.value = store.getLanguage;

  // 更新畫面寬度等級
  window.addEventListener("resize", (value) =>
    store.updateScreenSize(document.documentElement.clientWidth)
  );
  store.updateScreenSize(document.documentElement.clientWidth);
});

const drawer = ref(false);
const onDrawerExpended = function (e: any) {
  drawer.value = true;
};
const onMenuItemSeleted = function (e: any) {
  drawer.value = false;
};
</script>

<template>
  <div>
    <el-container style="height: 100vh" direction="vertical">
      <el-header>
        <MainHeader @onDrawerExpended="onDrawerExpended" />
      </el-header>
      <el-container>
        <el-aside class="desktop_view" width="250px">
          <MainSideMenu />
        </el-aside>
        <el-main>
          <slot />
        </el-main>
      </el-container>
    </el-container>
  </div>
  <el-drawer v-model="drawer" direction="ttb" :with-header="false" size="80%">
    <MainSideMenu @onMenuItemSeleted="onMenuItemSeleted">
      <UtilLangDropdown @onLanguageSeleted="onMenuItemSeleted" class="dropdownUtilLang-item" />
    </MainSideMenu>
  </el-drawer>
</template>

<style>
.el-main {
  background-color: #f3f3f3;
  padding: 0px;
  overflow-y: hidden;
}

@media (max-width: 400px) {
  .el-header {
    --el-header-padding: 0 0 0 0px ;
  }
}

.el-drawer__body {
  margin: 0px;
  padding: 0;
  position: relative;
}

.dropdownUtilLang-item {
  margin: 30px;
}
</style>
