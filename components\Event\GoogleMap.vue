<template>
  <el-button :style="{ minWidth: '80px', fontSize: '18px', color: '#111111' }" type="info" plain size="large" @click=handleMapClick :icon="MapLocation"></el-button>
</template>

<script setup lang="ts">
import { StoreLocation } from "~~/models/event";
import { MapLocation } from '@element-plus/icons-vue'
const props = defineProps({
  storeName: {//店家名稱
    type: String,
    required: true,
  },
  location: {//經緯度
    type: Object as PropType<StoreLocation>,
    required: true,
  }
});

async function handleMapClick(event: MouseEvent) {//點擊按鈕時
  event.stopPropagation();

  // 使用 props 中的 storeName 和 location
  const storeName = props.storeName;
  const { latitude, longitude } = props.location;

  // 生成 Google 地圖的 URL 再找找看能不能經緯度定位時 搜尋框顯示storeName
  const googleMapUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;

  // 在新分頁中打開 Google 地圖
  window.open(googleMapUrl, '_blank');
};
</script>

<style scoped>
.el-button {
  border-radius: 8px; /* 圓角 */
}
</style>