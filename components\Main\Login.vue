<template>
  <el-dialog :modelValue="visible" custom-class="login-dialog" :before-close="handleClose" append-to-body>
    <div class="center-container">
      <span v-if="!isRegister" class="loginTitle">{{ $t("menu.login") }}</span>
      <span v-if="isRegister" class="loginTitle">{{ $t("menu.register") }}</span>
    </div>
    <div class="btnContainer">
      <a :href="facebookLoginUrl">
        <img src="..\..\public\fb.jpg" alt="Sign in with Facebook" class="loginBtn">
      </a>
      <div class="spacer"></div>
      <vue-apple-login class="third-party-button" :onSuccess="onAppleSigninSuccess" data-mode="logo-only"
        :onFailure="onFailure"></vue-apple-login>
      <div class="spacer"></div>
      <img src="..\..\public\email.jpg" alt="Sign in with Email" class="loginBtn2" @click="handleEailClick">
    </div>
    <div class="center-container">
      <div class="inline-container">
        <span v-if="!isRegister" class="">{{ $t("menu.notRegister") }}?</span>
        <span v-if="!isRegister" class="clickable" @click="handleRegisterClick">{{ $t("menu.goRegister") }}!</span>
        <span v-if="isRegister" class="">{{ $t("menu.registed") }}?</span>
        <span v-if="isRegister" class="clickable" @click="handleRegisterClick">{{ $t("menu.goLogin") }}!</span>
      </div>
    </div>
    <el-divider v-if="emailFrom" />
    <el-form class="center-form" :model="loginForm" @submit="onSubmit" v-if="emailFrom">
      <el-space direction="vertical">
        <el-input style="width: 260px" :disabled=waitVerify v-model="loginForm.email" @keyup.enter="onSubmit"
          placeholder="Email" />
        <el-input style="width: 260px" :disabled=waitVerify v-model="loginForm.password" @keyup.enter="onSubmit"
          type="password" :placeholder="$t('menu.password')" />
        <span v-if="isRegister" class="register-text">{{ $t("menu.emailRule") }}</span>
        <el-button type="primary" v-if="isRegister" :disabled=waitVerify @click="onSubmit">{{
    $t("menu.sendVerifyCode")
  }}</el-button>
        <el-button type="primary" v-if="!isRegister" @click="onSubmit">{{ $t("menu.login") }}</el-button>
        <span v-if="waitVerify" class="register-text">{{ $t("menu.goEmail") }}{{ loginForm.email }}</span>
        <span v-if="waitVerify" class="register-text">{{ $t("menu.emailNotReceive") }}</span>
        <el-input style="width: 260px" v-if="waitVerify" v-model="loginForm.code" @keyup.enter="onSubmit"
          :placeholder="$t('menu.code')" />
        <el-button type="primary" v-if="waitVerify" @click="onSubmit">{{ $t("menu.verify") }}</el-button>
      </el-space>
    </el-form>
    <div class="center-container">
      <div class="terms">
        <el-link type="info"  @click="navigateToTerms" target="_blank">{{ $t('account.terms') }}</el-link>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
// @ts-ignore
import queryString from 'query-string';//我也不知道為啥報錯 不理他

const config = useRuntimeConfig();
const facebookLoginUrl = ref("");
const loading = ref(false);
const isRegister = ref(false);//是否是註冊模式
const emailFrom = ref(false);//是否開啟email表格
const waitVerify = ref(false);//等待驗證
const resetPassword = ref(false);//更改密碼
const { t } = useI18n();
let store = globalStore();
const loginForm = reactive({ email: "", password: "", code: "" });

const props = defineProps<{//從父組件來,
  visible: {
    type: Boolean,
    required: true,
  }
}>();

const emit = defineEmits(['update:visible']);//返回父組件
function handleEailClick() {// email註冊/登入
  emailFrom.value = !(emailFrom.value);
  waitVerify.value = false;
};
function handleRegisterClick() {// email註冊/登入
  isRegister.value = !(isRegister.value)
};

function handleClose(done: () => void) {// 關閉 dialog 的方法
  emit('update:visible', false);
  done();
};
onMounted(() => {
  const FB_DIALOG_LINK = "https://www.facebook.com/v17.0/dialog/oauth";
  const params = queryString.stringify({
    client_id: config.public.fbClientId,
    redirect_uri: `${config.public.webBaseUrl}/login/facebook`,
    scope: ["public_profile", "email"].join(","),
  });
  facebookLoginUrl.value = `${FB_DIALOG_LINK}?${params}`;
});

const onSubmit = async function (e: Event) {
  e.preventDefault();
  console.log(isRegister.value);
  console.log(waitVerify.value);
  console.log(resetPassword.value);
  if (loginForm.email === "" || loginForm.password === "" || loginForm.password.length < 6)
    return;//沒有輸入東西 不要動作
  if (!waitVerify.value)//第一次按下按鈕 不管啥狀態 都先檢查一遍是否有帳號 密碼是否正確
  {
    const checkResult = await store.signInCheck(loginForm.email.trim(), loginForm.password)
    if (checkResult.isExisted)//有帳號
    {
      if (checkResult.passwordCorrect)//密碼正確
      {//登入
        loading.value = true;
        await store.signInAndSaveCache(loginForm.email.trim(), loginForm.password)
          .then(() => {
          })
          .finally(() => {
            loading.value = false;
            let user = store.getCurrentUser;
            if (user) {//成功
              loading.value = false;
              window.location.reload();
            }
            else {//失敗
              loading.value = false;
              window.location.reload();
            }
          });
      }
      else {//改密碼 
        await store.forgot(loginForm.email.trim())
          .then(() => {
          })
          .finally(() => {
            resetPassword.value = true;
            waitVerify.value = true;
          });
      }
    }
    else {//註冊
      await store.register(loginForm.email.trim(), loginForm.password)
        .then(() => {
        })
        .finally(() => {
          waitVerify.value = true;
        });
    }
  }
  else//驗證模式
  {
    if (resetPassword.value)//改密碼後的登入
    {
      if (loginForm.code.length != 6)
        return;//沒有輸入東西 不要動作
      loading.value = true;
      await store.resetPassword(loginForm.email.trim(), loginForm.password, loginForm.code)
        .then(() => {
          resetPassword.value = false;
        })
        .finally(() => {
          loading.value = false;
          let user = store.getCurrentUser;
          if (user) {//成功
            loading.value = false;
            window.location.reload();
          }
          else {//失敗
            loading.value = false;
            window.location.reload();
          }
        });
    }
    else//註冊後的登入
    {
      if (loginForm.code.length != 6)
        return;//沒有輸入東西 不要動作
      loading.value = true;
      await store.verify(loginForm.email.trim(), loginForm.code)
        .then(() => {
        })
        .finally(() => {
          loading.value = false;
          let user = store.getCurrentUser;
          if (user) {//成功
            loading.value = false;
            window.location.reload();
          }
          else {//失敗
            loading.value = false;
            window.location.reload();
          }
        });
    }
  }
};

const onAppleSigninSuccess = async (data: any) => {
  console.log(data);
  await store
    .signInWithApple(data.userData.sub, data.authorization.code, data.userData.email)
    .then(() => {
      //navigateTo(store.getRedirect ?? "/home");
      //store.setRedirect(undefined);
    })
    .finally(() => {
      loading.value = false;
      window.location.reload();
    });
};

const onFailure = async (error: any) => {
  console.log(error);
};

const navigateToTerms = () => {
  navigateTo("/terms");
};
</script>

<style>
.login-dialog {
  max-width: 300px;
  width: 100%;
}

.login-dialog header {
  padding: 0px;
  /* 覆蓋 Element Plus 預設的 max-width */
}

.login-dialog :deep(.el-dialog + .el-dialog) {
  --el-dialog-width: 100% !important;
  /* 覆蓋 Element Plus 預設的 max-width */
}

.loginTitle {
  font-weight: bold;
  font-size: 24px;
  color: #000000;
  margin-bottom: 20px;
}

.btnContainer {
  display: flex;
  align-items: flex-start;
}

.spacer {
  width: 10px;
}

.third-party-button {
  height: 80px;
  width: 80px;
  cursor: pointer;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-direction: column;
}

.terms {
  margin-top: 5px;
}

#appleid-signin>div {
  width: auto !important;
  height: auto !important;
  min-width: 30px !important;
  max-width: none !important;
  min-height: 30px !important;
  max-height: none !important;
}

.loginBtn {
  max-width: 80px;
  cursor: pointer;
  border-radius: 15px;
}

.loginBtn2 {
  max-width: 80px;
  cursor: pointer;
  border-radius: 15px;
}

.inline-container {
  font-size: 15px;
  display: flex;
  align-items: center;
}

.clickable {
  cursor: pointer;
  color: #2766a9;
  /* 根據需要設置樣式 */
  margin-left: 5px;
  /* 為間距添加樣式 */
}
</style>