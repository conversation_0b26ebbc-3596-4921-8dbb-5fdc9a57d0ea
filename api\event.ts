import { EventTags, EventCard, EventDetail, PaymentCreateResult, Review, EventEdit, Participants } from "../models/event";
import axios from "./axios";
import { useI18n } from 'vue-i18n';
interface EventOptions {//活動相關的API很多有不同Call法，用這樣的方法避免一直重寫export const
  eventID?: string//活動ID拿來查活動詳情
  tagID?: string;//查詢活動列表時可以使用tag
  brandID?: string;//查詢活動列表時可以使用品牌
  teacherID?: string;//查詢活動列表時可以使用教師
  guestKey?: string;//未登入用戶需要用guestKey
  language?: string;//未登入用戶需要提供語言
  isPlatformEvent?: boolean;//是否是我們平台上辦的活動
  isPast?: boolean;//已過期活動
  isEdit?: boolean;//編輯模式
}

export const getEventTags = async (options: EventOptions = {}): Promise<EventTags[]> => {//活動標籤
  const { guestKey, language } = options;
  const payload: any = {};
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;

  return (await axios.post("/api/event/tag", payload)).data.result as EventTags[];
};

export const getEventCard = async (options: EventOptions = {}): Promise<EventCard[]> => {//活動列表
  const { tagID, brandID, teacherID, guestKey, language, isPast, isEdit } = options;
  const payload: any = {};
  if (tagID) payload.tagID = tagID;
  if (brandID) payload.brandID = brandID;
  if (teacherID) payload.teacherID = teacherID;
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;

  if (isPast)
    return (await axios.post("/api/brand/pastEvent/card", payload)).data.result as EventCard[];//過往活動
  else {
    if (isEdit)
      return (await axios.post("/api/brand/owner/event/card", payload)).data.result as EventCard[];//品牌編輯者的目前活動
    else
      return (await axios.post("/api/event/card", payload)).data.result.events as EventCard[];//一般使用者看的目前活動
  }
};

export const getEventDetail = async (options: EventOptions = {}): Promise<EventDetail> => {//活動詳細資料
  const { eventID, guestKey, language, isPlatformEvent } = options;
  const payload: any = {};
  if (eventID) payload.eventID = eventID;
  if (eventID) payload.eventWebIndex = eventID;
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;

  if (isPlatformEvent)//如果是已經結束的活動 要改call pastEvent/detail
    return (await axios.post("/api/event/detailV2", payload)).data.result as EventDetail;
  else
    return (await axios.post("/api/brand/pastEvent/detail", payload)).data.result as EventDetail;
};

export const eventPaymentCreate = async (eventID: String, ticketTypeID: number, email: string, userName: string): Promise<PaymentCreateResult> => {//創建一筆活動訂單 一定要登入
  return (await axios.post("/api/event/payment/create", { eventID: eventID, ticketTypeID: ticketTypeID, email: email, userName: userName })).data.result as PaymentCreateResult;
};

export const getReview = async (options: EventOptions = {}): Promise<Review[]> => {//取得活動、教師、品牌評論
  const { eventID, brandID, teacherID, guestKey, language } = options;
  const payload: any = {};
  if (eventID) payload.eventID = eventID;
  if (brandID) payload.brandID = brandID;
  if (teacherID) payload.teacherID = teacherID;
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;

  return (await axios.post("/api/event/review", payload)).data.result as Review[];
};

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}.${month}.${day}`;
}
export function formatEventTime(startTime: string, endTime: string, week: number): string {
  const { t } = useI18n();
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);

  const startHours = String(startDate.getHours()).padStart(2, '0');
  const startMinutes = String(startDate.getMinutes()).padStart(2, '0');
  const endHours = String(endDate.getHours()).padStart(2, '0');
  const endMinutes = String(endDate.getMinutes()).padStart(2, '0');

  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}.${month}.${day}`;
  };

  if (startDate.toDateString() === endDate.toDateString()) {//單日多日活動 顯示格式不同
    const weekDay = t(`event.weekdays.${week}`);
    return `${formatDate(startDate)}(${weekDay})${startHours}:${startMinutes}-${endHours}:${endMinutes}`;
  } else {
    return `${formatDate(startDate)} ${startHours}:${startMinutes} - ${formatDate(endDate)} ${endHours}:${endMinutes}`;
  }
}
export function formatEditDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份從 0 開始
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
}

export const addEvent = async (eventAdd: EventEdit): Promise<number> => {//新增活動
  const { store, brandID, teacherID, tagID, primaryLanguage, eventDetail, brandDetail, teacherDetail, photo, totalQuantity, eventTime, ticketType } = eventAdd;
  const response = await axios.post("/api/brand/owner/event/add", {
    store, brandID, teacherID, tagID, primaryLanguage, eventDetail, brandDetail, teacherDetail, photo, totalQuantity, eventTime, ticketType
  });
  return response.data.status as number;
}

export const addPastEvent = async (eventAddPast: EventEdit): Promise<number> => {//新增過往活動
  const { store, brandID, teacherID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, eventTime, ticketType } = eventAddPast;
  const response = await axios.post("/api/brand/owner/pastEvent/add", {
    storeName: store.storeName, address: store.address, brandID, teacherID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, earlyBirdEndTime: eventTime.earlyBirdEndTime, startTime: eventTime.startTime, endTime: eventTime.endTime, ticketType
  });
  return response.data.status as number;
}


export const editEvent = async (eventID: string, eventEdit: EventEdit, editStore: boolean): Promise<number> => {//編輯活動
  const { eventWebIndex, store, storeID, teacherID, tagID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, totalQuantity, eventTime, ticketType } = eventEdit;
  let response = null;
  if (editStore) {
    response = await axios.post("/api/brand/owner/event/edit", {
      eventID, eventWebIndex, store, storeID, teacherID, tagID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, totalQuantity, eventTime, ticketType
    });
  } else {
    response = await axios.post("/api/brand/owner/event/edit", {
      eventID, eventWebIndex, storeID, teacherID, tagID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, totalQuantity, eventTime, ticketType
    });
  }
  return response!.data.status as number;
}

export const editTextEvent = async (eventID: string, eventEdit: EventEdit): Promise<number> => {//編輯活動(僅文字)
  const { editingLanguage, eventDetail, brandDetail, teacherDetail, photo } = eventEdit;
  const response = await axios.post("/api/brand/owner/event/editText", { eventID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo });
  return response!.data.status as number;
}

export const editPastEvent = async (eventID: string, eventEditPast: EventEdit): Promise<number> => {//編輯過往活動
  const { store, brandID, teacherID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, eventTime, ticketType } = eventEditPast;
  const response = await axios.post("/api/brand/owner/pastEvent/edit", {
    eventID, eventWebIndex: eventEditPast.eventWebIndex, storeName: store.storeName, address: store.address, brandID, teacherID, editingLanguage, eventDetail, brandDetail, teacherDetail, photo, earlyBirdEndTime: eventTime.earlyBirdEndTime, startTime: eventTime.startTime, endTime: eventTime.endTime, ticketType
  });
  return response!.data.status as number;
}


export const eventParticipantsCheck = async (eventID: string): Promise<boolean> => {//檢查活動是否有人報名
  const response = await axios.post("/api/brand/owner/event/participantsCheck", { eventID });
  return response.data.result.hasParticipants as boolean;
}

export const eventParticipants = async (eventID: string): Promise<Participants> => {//查看報名情況
  const response = await axios.post("/api/brand/owner/event/participants", { eventID });
  return response.data.result as Participants;
}

export const removeEvent = async (eventID: string, remove: boolean): Promise<number> => {//上架下架活動
  const response = await axios.post("/api/brand/owner/event/remove", { eventID, remove });
  return response.data.status as number;
}

export const deleteEvent = async (eventID: string): Promise<number> => {//刪除活動
  const response = await axios.post("/api/brand/owner/event/delete", { eventID });
  return response.data.status as number;
}