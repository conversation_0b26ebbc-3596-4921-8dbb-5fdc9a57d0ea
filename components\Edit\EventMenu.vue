<template>
  <el-dropdown @command="handleCommand" @visible-change="handleVisibleChange" size="large">
    <span class="el-dropdown-link" @click.stop>
      <img src="../../public/_icons_/dropdown-icon.svg" alt="Custom Icon" style="width: 24px; height: 24px;" />
    </span>
    <template #dropdown>
      <el-dropdown-menu slot="dropdown" v-if="!props.isPast"><!--目前活動-->
        <el-dropdown-item class="dropdown-item" command="editEvent">{{ $t('brand.edit') }}</el-dropdown-item>
        <el-dropdown-item class="dropdown-item" command="getRegistrationList">{{ $t('brand.getRegistrationList')  }}</el-dropdown-item>
        <el-dropdown-item v-if="!hasParticipants && !props.eventCard.isRemoved" class="dropdown-item" command="downEvent">{{ $t('brand.downEven') }}</el-dropdown-item>
        <el-dropdown-item v-if="props.eventCard.isRemoved" class="dropdown-item" command="upEvent">{{ $t('brand.upEvent') }}</el-dropdown-item>
        <el-dropdown-item v-if="!hasParticipants" class="dropdown-item" command="deleteEvent">{{ $t('brand.deleteEvent') }}</el-dropdown-item>
      </el-dropdown-menu>
      <el-dropdown-menu slot="dropdown" v-else><!--過往活動-->
        <el-dropdown-item class="dropdown-item" command="editEvent">{{ $t('brand.edit') }}</el-dropdown-item>
        <el-dropdown-item v-if="props.eventCard.isPlatformEvent" class="dropdown-item" command="getRegistrationList">{{ $t('brand.getRegistrationList') }}</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
  <EditDialog :type=type :visible=isDialogVisible :eventCard="props.eventCard"
    @update:visible="isDialogVisible = $event">
  </EditDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { EventCard } from "~~/models/event";
import { eventParticipantsCheck } from "~~/api/event";
const props = defineProps<{
  eventCard: EventCard;//直接拿整個brandDetail
  isPast: boolean;//已經過期的活動
}>();
const hasParticipants = ref(true);//有人報名
const isDialogVisible = ref(false);
const type = ref(1);
const handleCommand = (command: string) => {
  switch (command) {
    case 'editEvent':
      editData();
      break;
    case 'getRegistrationList':
      getRegistrationList();
      break;
    case 'downEvent':
      downEvent();
      break;
    case 'upEvent':
      upEvent();
      break;
    case 'deleteEvent':
      deleteEvent();
      break;

  }
};

const handleVisibleChange = (async (visible: boolean) => {//點開時
  if (visible)
    hasParticipants.value = await eventParticipantsCheck(props.eventCard.eventID);//取得活動列表
  else
    hasParticipants.value = true;
});

const editData = () => {
  console.log('編輯資料');
  if (props.isPast) { //過往活動
    type.value = props.eventCard.isPlatformEvent ? 8 : 9;//自家:8 外來:9
  } else { // 目前活動
    type.value = hasParticipants.value ? 8 : 7;//有人報名:8 無人報名:7
  }
  isDialogVisible.value = true;
};
const getRegistrationList = () => {
  console.log('報名狀況');
  type.value = 10;
  isDialogVisible.value = true;
};
const downEvent = () => {
  console.log('下架活動');
  type.value = 11;
  isDialogVisible.value = true;
};
const upEvent = () => {
  console.log('重新上架');
  type.value = 12;
  isDialogVisible.value = true;
};
const deleteEvent = () => {
  console.log('刪除活動');
  type.value = 13;
  isDialogVisible.value = true;
};



</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
}

::v-deep .el-dropdown-menu__item {
  font-size: 16px;
  /* 調整字號 */
}
</style>