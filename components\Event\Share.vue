<template>
  <el-button :style="{ minWidth: '80px', fontSize: '18px', color: '#111111' }" type="info" plain size="large" @click="handleShareClick" :icon="Share"></el-button>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ElMessage } from 'element-plus';
import { Share } from '@element-plus/icons-vue'
const props = defineProps({
  eventID: {
    type: String,
    required: true,
  },
  language: {
    type: String,
    required: true,
  }
});

const { t } = useI18n();

async function handleShareClick(event: MouseEvent) {
  event.stopPropagation();
  const baseUrl = window.location.origin;
  const url = `${baseUrl}/event/${props.eventID}?language=${props.language}`;

  try {
    await navigator.clipboard.writeText(url);
    ElMessage({
      message: t('event.shareSuccess'),
      type: 'success',
    });
  } catch (err) {
    ElMessage({
      message: t('event.shareFailed'),
      type: 'error',
    });
  }
}
</script>
<style scoped>
.el-button {
  border-radius: 8px; /* 圓角 */
}
</style>