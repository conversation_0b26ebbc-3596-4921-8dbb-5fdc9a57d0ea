import {
  ScreenSize,
  breakPointLevel,
  getBreakpointLevel,
} from "~~/models/mediaQuery";
import { _ActionsTree, _GettersTree, defineStore } from "pinia";
import {
  deleteAccount,
  getGuestKey,
  getUserInfo,
  logout,
  signUp,
  verify,
  signIn,
  signInWithToken,
  signInWithApple,
  signInWithFb,
  setSystemLanguage,
  signInCheck,
  resetPassword,
  forgot,
  signInWithGoogle
} from "../api/user";

import { AxiosInstance } from "axios";
import { User, SignInCheck } from "../models/user";
import axios from "../api/axios";

interface GlobalState {
  user: User | undefined;
  language: string;
  currentRoute: string;
  screenSize: string;
  axios: AxiosInstance;
}

export const globalStore = defineStore("global", {
  state: (): GlobalState => {
    let initLanguage = localStorage.getItem("language") as string;//嘗試先取得已經設定過的語言
    if (!initLanguage)
      initLanguage = "zh"//沒有的話再用預設
    return {
      user: undefined,
      language: initLanguage,
      currentRoute: "home",
      screenSize: breakPointLevel[0],
      axios: axios,
    };
  },
  actions: {

    async signInCheck(email: String, password: String): Promise<SignInCheck> {
      let guestKey = await getGuestKey();
      const language = this.language;
      return await signInCheck(guestKey, email, password, language);
    },
    async register(email: String, password: String): Promise<Number> {
      let guestKey = await getGuestKey();
      const language = this.language;
      return await signUp(guestKey, email, password, language);
    },
    async verify(email: String, code: String): Promise<User> {
      let guestKey = await getGuestKey();
      const language = this.language;
      let user = await verify(guestKey, email, code, language);
      this.user = user;
      localStorage.setItem("user", JSON.stringify(user));
      return user;
    },
    async forgot(email: String): Promise<Number> {
      let guestKey = await getGuestKey();
      return await forgot(guestKey, email)
    },
    async resetPassword(email: String, password: String, code: String): Promise<User | undefined> {
      const status = await resetPassword(email, password, code);//先更改密碼
      if (status === 0)//再登入 
      {
        let guestKey = await getGuestKey();
        const language = this.language;
        let user = await signIn(guestKey, email, password, language);
        this.user = user;
        localStorage.setItem("user", JSON.stringify(user));
        return user;
      }
    },

    async signInAndSaveCache(email: String, password: String): Promise<User> {
      let guestKey = await getGuestKey();
      const language = this.language;
      let user = await signIn(guestKey, email, password, language);
      this.user = user;
      localStorage.setItem("user", JSON.stringify(user));
      return user;
    },
    async signInWithToken(token: string): Promise<User> {
      let user = await signInWithToken(token);
      this.user = user;
      localStorage.setItem("user", JSON.stringify(user));
      return user;
    },
    async signInWithFb(uid: String, userName: String): Promise<User> {
      let guestKey = await getGuestKey();
      const language = this.language;
      let user = await signInWithFb(guestKey, uid, language, userName);
      this.user = user;
      localStorage.setItem("user", JSON.stringify(user));
      return user;
    },
    async signInWithApple(uid: string, authCode: string, email: string): Promise<User> {
      let guestKey = await getGuestKey();
      const language = this.language;
      let user = await signInWithApple(guestKey, uid, authCode, language, email);
      this.user = user;
      localStorage.setItem("user", JSON.stringify(user));
      return user;
    },
    async signInWithGoogle(code:string): Promise<User> {
      let guestKey = await getGuestKey();
      const language = this.language;
      let user = await signInWithGoogle(guestKey, code, language);
      this.user = user;
      localStorage.setItem("user", JSON.stringify(user));
      return user;
    },
    async signOutAndClearCache() {
      await logout();
      localStorage.removeItem("user");
      this.user = undefined;
      return this.user;
    },

    async deleteAccount() {
      try {
        await deleteAccount(); // 這是刪除帳號的 API 函數
        console.log("Account deleted successfully!");
      } catch (error) {
        // 刪除失敗後的處理
        console.error("Failed to delete account:", error);
      } finally {
        localStorage.removeItem("user");
        this.user = undefined;
        return this.user;
      }
    },

    async clearUserCache() {
      localStorage.removeItem("user");
      this.user = undefined;
    },

    async updateCache(userID: string, accessKey: string) {
      localStorage.setItem(
        "user",
        JSON.stringify({ userID: userID, accessKey: accessKey })
      );
      const userInfo = await getUserInfo(userID);
      const user: User = {
        userID: userID,
        accessKey: accessKey,
        userName: userInfo.userName,
        userPhoto: userInfo.userPhoto,
        userEmail: userInfo.email,
      };
      localStorage.setItem("user", JSON.stringify(user));
      this.user = user;
      return this.user;
    },
    setCurrentRoute(route: string) {
      this.currentRoute = route;
    },
    async setLanguage(language: string) {
      if (this.user != undefined) {//已登入的情況 才切換系統語言
        const result = await setSystemLanguage(language);
        this.language = language;//等回傳再切語言
        localStorage.setItem(
          "language", language
        ); 
      }
      else//未登入
      {
        this.language = language;
        localStorage.setItem(
          "language", language
        );
      }
      //todo:有登入的情況下 每一次切換頁面都會call一次setSystemLanguage 有空修正
      //問題在於auth.ts與unauth.ts中的store.setLanguage(toLang);
    },
    updateScreenSize(dimension: number) {
      this.screenSize = getBreakpointLevel(dimension);
    },
    setRedirect(url: string | undefined) {
      if (url) localStorage.setItem("redirect", url);
      else localStorage.removeItem("redirect");
    },
  },
  getters: {
    getCurrentUser(): User {
      let user = JSON.parse(localStorage.getItem("user") as string);
      this.user = user;
      return this.user as User;
    },
    getCurrentRoute(): string {
      // console.log(this.currentRoute);
      return this.currentRoute;
    },
    getLanguage(): string {
      return this.language;
    },
    getCacheLanguage(): string {//只在初始化global.ts(也就是這裡的this)時用，方便從第三方網頁回來時還保持原先的語言選項，不要在middleWare用，會出錯
      let language = localStorage.getItem("language") as string;
      return language;
    },
    getScreenSize(): string {
      return this.screenSize;
    },
    getRedirect(): string | undefined {
      return localStorage.getItem("redirect") as string | undefined;
    },
    axios(): AxiosInstance {
      return this.axios;
    },
  },
});
