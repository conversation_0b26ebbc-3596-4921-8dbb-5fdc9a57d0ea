export interface EventCard {//活動列表 總共有三隻 給使用者的event/card 給過去活動的pastEvent/card 給管理者的owner/event/Card
    eventID: string;
    storeID: string;
    brandID: string;
    teacherID: string;
    photo: string;
    eventName: string;
    storeName: string;
    teacherName: string;
    teacherNationality: string;
    brandName: string;
    address: string;
    ticketType: TicketType[];//onwer沒有
    paymentTicket: TicketType;//onwer沒有
    totalQuantity: number;
    participants: number;
    location: StoreLocation;
    isApply: boolean;
    eventWebIndex: string;
    brandWebIndex: string;
    teacherWebIndex: string;
    localStartTime: string;
    localEndTime: string;
    localEarlyBirdEndTime: string;
    week: number;
    isPlatformEvent: boolean;//是否是平台上辦的活動 past才有
    isRemoved: boolean;//onwer才有 是否下架中
}

export interface EventDetail extends EventCard {//活動詳情v2
    tagID: string;
    oriEventDetail: TransEventDetail;
    transEventDetail: TransEventDetail;
    oriTeacherDetail: TransTeacherDetail;
    transTeacherDetail: TransTeacherDetail;
    oriBrandDetail: TransBrandDetail;
    transBrandDetail: TransBrandDetail;
    oriLanguage: string;
    transLanguage: string;
    isEnd: boolean;
    pastPhotoList: PastPhotoList[];
}

export interface TicketType {
    ticketTypeID: number;
    ticketTypeName: string;
    price: number;
    originalPrice: number;
}

export interface StoreLocation {
    longitude: number;
    latitude: number;
}

export interface TransEventDetail {
    name: string;
    description: string;
    feature: string;
    storeDescription: string;
    meals: string;
}

export interface TransTeacherDetail {
    name: string;
    description: string;
}

export interface TransBrandDetail {
    name: string;
    description: string;
}

export interface Store {//活動地點
    storeName: string,
    address: string,
    location: StoreLocation,
    countryCode: string
}

export interface PastPhotoList {
    uuid: string;
    url: string;
}

export interface PaymentCreateResult {//創建綠界訂單
    MerchantID: string;
    MerchantTradeNo: string;
    MerchantTradeDate: string;
    PaymentType: string;
    TotalAmount: number;
    TradeDesc: string;
    ItemName: string;
    ReturnURL: string;
    ChoosePayment: string;
    CheckMacValue: string;
    EncryptType: number;
    NeedExtraPaidInfo: string;
    Language: string | undefined;
}

export interface EventTags {//活動標籤
    tagID: string;
    label: string;
}

export interface Review {//活動、品牌、教師的評價
    createTime: string;
    userName: string
    eventName: string;
    teacherRating: number;
    eventRating: number;
    storeRating: number;
    comment: string
}

export interface EventEdit {//新增、編輯活動 包含1新增目前活動 2編輯目前活動 3編輯目前活動(僅文字) 4新增過往活動 5編輯過往活動
    eventWebIndex: string,
    store: Store,
    storeID: string,
    brandID: string,
    teacherID: string,
    tagID: string,
    primaryLanguage: string,
    editingLanguage: string,
    eventDetail: TransEventDetail,
    brandDetail: TransBrandDetail,
    teacherDetail: TransTeacherDetail,
    photo: string,
    totalQuantity: number,
    eventTime: {
        earlyBirdEndTime: string,
        startTime: string,
        endTime: string
    },
    ticketType: TicketType[],
}

export interface NotAttendedList {//尚未出席
    userID: string;
    userName: string;
    email: string;
    facePhoto: string;
    ticketTypeID: number;
}

export interface AttendedList extends NotAttendedList {//已出席
    claimTime: string;
    localClaimTime: string;
}

export interface Participants{
    attendedList: AttendedList[];
    notAttendedList: NotAttendedList[];
}