<template>
  <el-button type="primary" @click=handleReviewClick> {{ $t('brand.seeScore') }} </el-button>
  <el-dialog :modelValue="isReviewVisible" :width="'340px'" :before-close="handleClose" append-to-body>
    <div class="reviewDialog-content">
      <el-col v-for="(review) in reviews" :key="review.createTime" class="column-spacing">
        <div class="review-content">
          <div class="title" >{{ review.userName }} - {{ review.eventName }}</div>
          <div>{{ formatDate(review.createTime) }}</div>
          <div>
            {{ $t('brand.teacherRating') }}
            <span v-for="n in review.teacherRating" :key="'teacher-' + n" class="star">★</span>
            <span v-for="n in 5 - review.teacherRating" :key="'teacher-empty-' + n" class="star">☆</span>
          </div>
          <div>
            {{ $t('brand.eventRating') }}
            <span v-for="n in review.eventRating" :key="'event-' + n" class="star">★</span>
            <span v-for="n in 5 - review.eventRating" :key="'event-empty-' + n" class="star">☆</span>
          </div>
          <div>
            {{ $t('brand.envRating') }}
            <span v-for="n in review.storeRating" :key="'env-' + n" class="star">★</span>
            <span v-for="n in 5 - review.storeRating" :key="'env-empty-' + n" class="star">☆</span>
          </div>
          <div>{{ $t('brand.commentText') }}</div>
          <div class="review-text">{{ review.comment }}</div>
        </div>
        <hr class="divider" > 
      </el-col>
    </div>
  </el-dialog>

</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { useRoute } from 'vue-router';
import { getReview, formatDate } from "../../api/event";
import { Review } from "~~/models/event";
import { getGuestKey } from "../../api/user";
import { User } from "~~/models/user";
const props = defineProps({
  brandID: {//品牌
    type: String
  },
  teacherID: {//教師
    type: String
  }
});

const { t } = useI18n();
const reviews = ref<Review[]>();
const isReviewVisible = ref(false);

const route = useRoute();
let language = ref(route.query.language || 'default');
let user: User;
const store = globalStore();
user = store.getCurrentUser;
async function handleReviewClick() {//未登入與已登入有不同的call法
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    reviews.value = await getReview({ guestKey: guestKey, brandID: props.brandID, teacherID: props.teacherID, language: languageString });
    if(reviews.value.length > 0)//有評論才顯示 沒有就不動作
      isReviewVisible.value = true;
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    reviews.value = await getReview({ brandID: props.brandID, teacherID: props.teacherID });
    if(reviews.value.length > 0)//有評論才顯示 沒有就不動作
      isReviewVisible.value = true;
  }
};

// 關閉 dialog 的方法
function handleClose(done: () => void) {
  isReviewVisible.value = false;
  done();
};
watch(
  () => route.query.language,
  (newLanguage) => {
    language.value = newLanguage || 'default';
  }
);
</script>

<style scoped>
.star {
  color: gold;
  margin-right: 5px;
}
.review-content{
  line-height: 1.75;
  color: #333;
}
.title{
  font-size: 16px; 
  font-weight: bolder; 
}
.review-text{
  margin-top: 8px;
  line-height: 1.1;
}
.divider{
  margin-top: 10px;
  margin-bottom: 15px;
}
</style>