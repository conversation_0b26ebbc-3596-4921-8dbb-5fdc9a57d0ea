<template>
  <el-button v-if="user != null" type="danger" @click=handleDeleteClick>{{ $t('account.delete_account') }}</el-button>
  <el-dialog v-model="isDeleteVisible" :width="'300px'" :title="$t('account.delete_account?')"
    @close="handleClose">
    <p>{{ $t('account.delete_waring1') }}</p>
    <p>{{ $t('account.delete_waring2') }}</p>
    <br>
    <el-form :model="deleteCheckForm" :inline="true" label-width="200px" label-position="top">
      <el-form-item :label="$t('account.enter_userName')">
        <el-input v-model="deleteCheckForm.userName" :placeholder="store.getCurrentUser.userName"
          autocomplete="off"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $t('account.delete_cancel') }}</el-button>
      <el-button type="danger" @click="checkDeleteAccount">{{ $t('account.delete_account') }}</el-button>
    </span>
  </el-dialog>

</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { useRoute } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus'
const i18n = useI18n();
const isDeleteVisible = ref(false);
const store = globalStore();
const route = useRoute();
const user = store.getCurrentUser;
async function handleDeleteClick() {//點擊按鈕時 跳出刪除帳號dialog
  isDeleteVisible.value = true;
  console.log("點了")
};

// 關閉 dialog 的方法
function handleClose() {
  deleteCheckForm.userName = '';
  isDeleteVisible.value = false;
};

const deleteCheckForm = reactive({
  userName: ''
})

async function checkDeleteAccount() {
  // 檢查輸入的 userName 是否正確
  if (deleteCheckForm.userName === store.getCurrentUser.userName) {
    handleClose();
    await store.deleteAccount();//刪帳號
    ElMessage({
      message: i18n.t('account.account_deleted'),
      type: 'success',
    })
    navigateTo("/login");
  } else {
    // 輸入的 userName 不正確
    ElMessageBox.alert('', i18n.t('account.account_incorrect'), { showConfirmButton: false, })
  }
}
</script>

<style scoped>
.el-button {
  border-radius: 4px;
  /* 圓角 */
}

</style>