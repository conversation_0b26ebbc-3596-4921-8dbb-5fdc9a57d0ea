<script lang="ts" setup>
import { watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getTeacherCard, } from "../../api/teacher";
import { getGuestKey } from "../../api/user";
import { globalStore } from "../../stores/global";
import { TeacherCard } from "~~/models/teacher";
import { User } from "~~/models/user";
const route = useRoute();
const router = useRouter();
const store = globalStore();
const language = ref(route.query.language || 'default');
const loading = ref(true);
const TeacherCards = ref<TeacherCard[]>();
let user: User;
const props = defineProps({
  brandID: {//這邊傳進來有可能是brandID 有可能其實是brandWebIndex 要注意
    type: String,
    required: true,
  },
  isEdit: {//是否是編輯模式(從'我的品牌'進入)
    type: Boolean,
    required: true,
  }
});
onMounted(async () => {//進入畫面時
  await getTeacherList();//取得教師列表
  loading.value = false;
});

async function getTeacherList() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    TeacherCards.value = await getTeacherCard({ guestKey: guestKey, language: languageString, brandID: props.brandID });
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    TeacherCards.value = await getTeacherCard({ brandID: props.brandID });
  }
}

function handleCardClick(teacherCard: TeacherCard) {//點擊卡片時
  const basePath = props.isEdit ? '/teacherEdit' : '/teacher';//跳轉不同頁面
  const identifier = teacherCard.teacherWebIndex || teacherCard.teacherID;//優先使用webIndex
  router.push(`${basePath}/${identifier}?language=${language}`);
}
function handleDoNotThing(event: MouseEvent) {//點擊卡片時
  event.stopPropagation();
}
watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  //refreshComponent();
  loading.value = true; // 設置加載狀態
  getTeacherList(); // 重新獲取教師列表
  loading.value = false; // 加載完成
});

</script>
<template>
  <el-row>
    <el-col v-for="(teacherCard) in TeacherCards" :key="teacherCard.teacherID" :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="column-spacing">
      <el-card :body-style="{ padding: '0px' }" class="card" @click="handleCardClick(teacherCard)">
        <div class="image-wrapper">
          <img :src="teacherCard.photo" class="image" alt="活動照片" />
        </div>
        <div class="nameMenu-wrapper">
          <div class="content-wrapper" style="padding: 14px ">
            <span>{{ teacherCard.name }}</span><span>( {{ teacherCard.rating }}</span><span
              :style="{ color: 'gold' }">★</span><span>)</span>
          </div>
          <EditTeacherMenu v-if="props.isEdit" :teacherID=teacherCard.teacherID :teacherName=teacherCard.name :canDelete=!teacherCard.hasEvent :isInside=false @click.native.stop></EditTeacherMenu>
        </div>
        <div class="content-wrapper" style="padding: 0 0 0 14px">
          <span>{{ $t('teacher.nationality') }}：</span><span>{{ $t(`nationality.${teacherCard.nationality}`) }}</span>
          <!--維護捷徑 有新增國籍就要新增翻譯 規則是ISO 3166-1 alpha-2 {{ $t(`nationality.US`) }}-->
        </div>
        <div class="content-wrapper" style="padding: 0 0 14px 14px">
          <span>{{ $t('teacher.language') }}：</span><span>{{ $t(`Language.${teacherCard.primaryLanguage}`) }}</span>
          <!--維護捷徑 有新增語言就要新增翻譯 規則是ISO 639-1{{ $t(`Language.zh`) }}-->
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped>
.el-col {
  /*兩row之間的空格高度*/
  margin-bottom: 24px;
}

.column-spacing {
  /*column之間的空格寬度*/
  padding: 5px;
}

.card {
  border-radius: 10px;
  /*圓角 */
  overflow: hidden;
  cursor: pointer;
  /*顯示手指 */
  transition: transform 0.2s;
}

.card:hover {
  transform: scale(1.015);
  /* 放大效果 */
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-wrapper {
  width: 100%;
  aspect-ratio: 16 / 9;
  /* 設置圖片的寬高比 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}
.nameMenu-wrapper
{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-right: 15px;
}

.content-wrapper {
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 16px;
  /* 調整字體大小 */
  line-height: 1.5;
  /* 增加行距 */
}

.bold {
  font-weight: bold;
  /* 設置粗體 */
}

.button-container {
  display: flex;
  gap: 5px;
  /* 按鈕之間的間距 */
  margin-top: 10px;
  /* 與上方組件的間距 */
}

.button-container :deep(.el-button + .el-button) {
  margin-left: 0 !important;
  /* 覆蓋 Element Plus 預設的 margin-left */
}
</style>
