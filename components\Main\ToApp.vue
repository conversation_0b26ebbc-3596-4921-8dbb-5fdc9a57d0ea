<template>
  <el-dialog :modelValue="visible" custom-class="toAPP-dialog" :before-close="handleClose" append-to-body>
    <div class="dialog-content">
      <!-- 第一行 -->
      <div class="logo-section">
        <img src="/itut-logo.png" alt="Logo" class="logo">
      </div>
      <!-- 第二行 -->
      <div class="text-section">
        <div><span>{{ $t('main.downloadApp') }}</span></div>
        <div><span>{{ $t(translatedText) }}</span></div>
      </div>
      <!-- 第三行 -->
      <div class="store-section">
        <a :href="googlePlayUrl" target="_blank">
          <img src="/googlePlay.png" alt="Google Play" class="google-play-logo">
        </a>
        <a :href="appStoreUrl" target="_blank">
          <img src="/appStore.png" alt="App Store" class="app-store-logo">
        </a>
      </div>
      <!-- 第四行 -->
      <div class="slogan-section">
        <span>{{ $t('main.appSlogan') }}</span>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps<{//從父組件來
  text: {
    type: String,
    required: true,
  },
  visible: {
    type: Boolean,
    required: true,
  }
}>();
const emit = defineEmits(['update:visible']);//返回父組件
// 關閉 dialog 的方法
function handleClose(done: () => void ) {
  emit('update:visible', false);
  done();
};
const googlePlayUrl="https://play.google.com/store/apps/details?id=lab.italkutalk";
const appStoreUrl="https://apps.apple.com/tw/app/italkutalk/id1263409577";
const translatedText = computed(() => t(props.text as unknown as string ));
</script>

<style>
.toAPP-dialog {
  max-width: 500px;
  width: 100%;
}
.toAPP-dialog :deep(.el-dialog + .el-dialog) {
  --el-dialog-width: 100% !important;
  /* 覆蓋 Element Plus 預設的 max-width */
}
.dialog-content {
  text-align: center;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.logo {
  width: 250px;
  height: 50px;
  margin-right: 10px;
}

.text-section div {
  margin-top: 5px; /* 上下間距 */
  margin-bottom: 15px; /* 上下間距 */
  color :#000000
}

.text-section span {
  font-size: 28px; /* 字體大小 */
  font-weight: bold; /* 字體粗細 */
  line-height: 1; /* 行距 */
}
.store-section {
  display: flex;
  justify-content: center;
  gap: 0px;
  margin-bottom: 10px;
}

.google-play-logo {
  height: 70px; /* Google Play 圖片高度 */
  width: auto; /* 自動調整寬度以保持比例 */
}

.app-store-logo {
  height: 50px; /* App Store 圖片高度 */
  width: auto; /* 自動調整寬度以保持比例 */
  margin-top: 9px;
}

.slogan-section {
  font-size: 24px;
  color: #000000;
}
</style>