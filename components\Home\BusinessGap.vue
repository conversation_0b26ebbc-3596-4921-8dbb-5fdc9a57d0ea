<template>
  <div class="appIntro-page">
    <!-- APP 下載卡片 -->
    <el-card class="appDownload-card">
      <section class="sec-app">
        <div class="main-pic">
          <img src="..\..\public\comming-soon.png" alt="bluenet_icon" />
        </div>
        <div class="main-description">
          <span class="text-content">{{ $t('home.contactInformation') }}</span>
          <div class="sub-description">
            <span class="subtext-content">{{ $t('home.email') }}</span>
            <span class="subtext-content">{{ $t('home.phone') }}</span>
            <span class="subtext-content">{{ $t('home.address') }}</span>
          </div>
        </div>
      </section>
    </el-card>
  </div>
</template>

<style scoped>
.text-content {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  color: #333333;
  /* 文字內文顏色 */
  font-weight: bold;
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.5;
  /* 行距 */
  font-size: 26px;
  /* 字體大小 */
}

.subtext-content {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  color: #333333;
  /* 文字內文顏色 */
  font-weight: bolder;
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.5;
  /* 行距 */
  font-size: 18px;
  /* 字體大小 */
}

.el-card {
  --el-card-border-color: var(--el-border-color-light);
  --el-card-border-radius: 4px;
  --el-card-padding: 0px;
  --el-card-bg-color: var(--el-fill-color-blank);
}

.appIntro-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0px 20px 0 20px;
  max-height: 150px;
  /* 限制高度 */
}

.appDownload-card {
  padding: 0px;
  background: linear-gradient(to right, #c7dff0, #44acf7);
  /* 背景漸層 */
}

.sec-app {
  display: flex;
  align-items: center;
  padding: 0px;
}

.main-pic {
  display: flex;
  align-items: center;
  max-height: 100%;
  /* 限制高度為父容器的高度 */
}

.main-pic img {
  max-height: 150px;
  /* 確保圖片高度不超過父容器 */
  max-width: 100%;
  /* 確保圖片寬度不超過父容器 */
  height: auto;
  /* 保持比例 */
  width: auto;
  /* 保持比例 */
}

.main-description {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  margin-left: 20px;
  /* 與圖片保持距離 */
}

.sub-description {
  display: flex;
  flex-direction: column;
  height: 100%;
}

@media (max-width: 770px) {
  .text-content {
    font-size: 22px;
  }
  .subtext-content {
  font-size: 16px;
  /* 字體大小 */
}
}
@media (max-width: 450px) {
  .text-content {
    font-size: 18px;
  }
}

@media (max-width: 800px) {
  .appDownload-content {
    flex-direction: column;
  }

  .main-description {
    margin-left: 0px;
  }
  .appIntro-page {
    max-height: 175px;
  }
}

@media (max-width: 500px) {
  .appIntro-page {
    padding: 0px;
    max-height: 250px;
  }
  .main-pic{
    display: none;
  }
  .main-description{
    padding: 20px;
  }
}

@media (max-width: 1000px) {
  .el-card {
    --el-card-border-color: var(--el-border-color-light);
    --el-card-border-radius: 4px;
    --el-card-padding: 0px;
    --el-card-bg-color: var(--el-fill-color-blank);
  }

}

@media (min-width: 1500px) {
  .appIntro-page {
    padding-right: 100px;
  }
}

@media (min-width: 1600px) {
  .appIntro-page {
    padding-right: 200px;
  }
}

@media (min-width: 1700px) {
  .appIntro-page {
    padding-right: 300px;
  }
}
</style>