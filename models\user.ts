export interface User {
  accessKey: string;
  userID: string;
  userName: string;
  userPhoto: string;
  userEmail: string;
}

export interface AuthPayload {
  accessKey: string;
  userID: string;
  timestamp: EpochTimeStamp;
}

export interface Profile{
  userID: string;
  userName: string;
  userPhoto: string;
}

export interface SystemLanguage{
  systemLanguage:	string;
  systemLanguageIndex:	number;
}

export interface SignInCheck {
  isExisted: Boolean;
  passwordCorrect: Boolean;
}

export interface Member {
  result: Boolean;
  memberInfo: MemberInfo;
}

export interface MemberInfo {
  memberStart: number;
  memberEnd: number;
}
