<script lang="ts" setup>
import { reactive, onMounted } from "vue";
import { useRoute } from 'vue-router';
definePageMeta({
  middleware: ["check-browser","unauth"],
});
const route = useRoute();
const brandID = route.params.id;
onMounted(async () => {//進入畫面時
  console.log(brandID);//現在路由的ID部分 有可能是brandID或是brandIndex
});

</script>
<template>
  <BrandDetailPage :brandID=brandID :isEdit=false />
</template>