<script setup lang="ts">
import { useRoute } from 'vue-router';
import { useI18n } from "vue-i18n";
const { t } = useI18n()
watchEffect(() => {
  useHead({
    title: `${t('menu.toAPP')} - ${t('home.title')}`,
  });
});
definePageMeta({
  middleware: ["check-browser","unauth"]
});

const route = useRoute();
let language = ref(route.query.language || 'default');
watch(
  () => route.query.language,
  (newLanguage) => {
    language.value = newLanguage || 'default';
  }
);
</script>

<template>
  <HomeAppDownload :language="language"></HomeAppDownload>
  <HomeGap></HomeGap>
  <HomeAppIntro1 :language="language"></HomeAppIntro1>
  <HomeAppIntro7 :language="language"></HomeAppIntro7>
  <!--<HomeAppIntro2 :language="language"></HomeAppIntro2>-->
  <HomeAppIntro3 :language="language"></HomeAppIntro3>
  <HomeAppIntro4 :language="language"></HomeAppIntro4>
  <HomeAppIntro5 :language="language"></HomeAppIntro5>
  <HomeAppIntro6 :language="language"></HomeAppIntro6>
  <HomeAppIntro8 :language="language"></HomeAppIntro8>
  <HomeBusinessGap></HomeBusinessGap>
  <HomeTrademark></HomeTrademark>
  <HomePatent></HomePatent>
</template>

<style scoped>
</style>