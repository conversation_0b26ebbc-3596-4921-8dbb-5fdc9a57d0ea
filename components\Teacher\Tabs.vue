<template>
  <div class="tags-div">
    <el-tabs v-model="selectTag" class="event-tabs" stretch="true" :value="0">
      <el-tab-pane :label="$t('event.newEvent')" name="0"></el-tab-pane>
      <el-tab-pane :label="$t('event.oldEvent')" name="1"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, watchEffect, onMounted } from 'vue';

const selectTag = ref("0");

const emit = defineEmits(['update:selectTag']);

watch(() => selectTag.value, async () => {//切換標籤時 回傳資料
  emit('update:selectTag', selectTag.value);//其實也不算啥ID 就是mode的意思吧 0 現在活動 1 往期活動 我用String來存是因為number會出Bug
});

</script>

<style scoped>
.event-tabs {
  color: #555555;
  font-size: 32px;
  font-weight: 600;
  --el-tabs-header-height: 50px;
}

.el-tabs.el-tabs--top.event-tabs {
  background-color: #ffffff;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 10px;
}
</style>