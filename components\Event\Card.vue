<script lang="ts" setup>
import { watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getEventCard, formatDate, formatEventTime } from "../../api/event";
import { getGuestKey } from "../../api/user";
import { globalStore } from "../../stores/global";
import { EventCard } from "~~/models/event";
import { User } from "~~/models/user";
const route = useRoute();
const router = useRouter();
const store = globalStore();
const language = ref(route.query.language || 'default');
const loading = ref(true);
const eventCards = ref<EventCard[]>();
let user: User;
const props = defineProps({
  tagID: {//活動標籤
    type: String
  },
  brandID: {//品牌
    type: String
  },
  teacherID: {//教師
    type: String
  },
  isPast: {//是否是過往活動
    type: Boolean,
    required: true
  },
  isEdit: {//是否是編輯模式
    type: Boolean,
    default: false
  }
});
const tagID = computed(() => props.tagID || '');
const brandID = computed(() => props.brandID || '');
const teacherID = computed(() => props.teacherID || '');

onMounted(async () => {//進入畫面時
  await getEventList();//取得活動列表
  loading.value = false;

});

async function getEventList() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    eventCards.value = await getEventCard({ guestKey: guestKey, language: languageString, tagID: tagID.value, brandID: brandID.value, teacherID: teacherID.value, isPast: props.isPast });
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    eventCards.value = await getEventCard({ tagID: tagID.value, brandID: brandID.value, teacherID: teacherID.value, isPast: props.isPast, isEdit: props.isEdit });
  }
}

function handleCardClick(eventCard: EventCard) {//點擊卡片時
  if(eventCard.isRemoved)//下架中活動無法點擊
    return
  const basePath = props.isPast ? (eventCard.isPlatformEvent ? '/event' : '/pastEvent') : '/event'; // 如果是過往活動，有可能去到不同路由
  const eventIndex = eventCard.eventWebIndex || eventCard.eventID;//有index先用index
  router.push(`${basePath}/${eventIndex}?language=${language}`);
}

function handleDoNotThing(event: MouseEvent) {//點擊已額滿時
  event.stopPropagation();
}
watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  //refreshComponent();
  loading.value = true; // 設置加載狀態
  if (language.value != 'default')
    getEventList(); // 重新獲取活動列表
  loading.value = false; // 加載完成
});
watch(() => tagID.value, async (newID) => {//標籤更新時會重抓資料
  loading.value = true; // 設置加載狀態
  getEventList(); // 重新獲取活動列表
  loading.value = false; // 加載完成
});

</script>
<template>
  <el-row>
    <el-col v-for="(eventCard) in eventCards" :key="eventCard.eventID" :xs="24" :sm="12" :md="12" :lg="12" :xl="12"
      class="column-spacing">
      <el-card :body-style="{ padding: '0px' }" class="card" @click="handleCardClick(eventCard)">
        <div class="image-wrapper">
          <img :src="eventCard.photo" class="image" alt="活動照片" />
          <div v-if="eventCard.isRemoved" class="overlay">
            {{ $t("event.ieRemoved") }}
          </div>
        </div>
        <div class="content-wrapper" style="padding: 14px">
          <div class="nameMenu-wrapper">
            <div><span class="bold">{{ $t("event.theme") }}：</span>{{ eventCard.eventName }}</div>
            <EditEventMenu v-if="$props.isEdit" :eventCard="eventCard" :isPast="props.isPast"></EditEventMenu>
          </div>
          <div v-if="eventCard.teacherName" ><span
              class="bold">{{ $t("event.teacher") }}：</span>{{ eventCard.teacherName
            }}</div>
          <div ><span class="bold">{{ $t("event.brand")
              }}：</span>{{ eventCard.brandName }}</div>
          <div v-if="!eventCard.teacherName"><span class="bold">{{ $t("event.location") }}：</span>{{ eventCard.storeName
            }}</div>
          <div><span class="bold">{{ $t("event.address") }}：</span>{{ eventCard.address }}</div>
          <div><span class="bold">{{ $t("event.eventTime") }}：</span>{{ formatEventTime(eventCard.localStartTime,
      eventCard.localEndTime, eventCard.week) }}</div>
          <!--<div><span class="bold">{{ $t("event.earlyBirdDeadline") }}：</span>{{
      formatDate(eventCard.localEarlyBirdEndTime)
    }}</div> -->
          <div v-if="!isPast && !isEdit" class="button-container"><!--給一般使用者看的-->
            <template v-if="eventCard.totalQuantity <= eventCard.participants">
              <el-button disabled
                :style="{ minWidth: '150px', fontSize: '16px', color: '#FF0000', borderRadius: '8px' }" type="info"
                plain size="large" @click=handleDoNotThing>{{ $t("event.full") }} </el-button>
            </template>
            <template v-else-if="user === null"><!--未登入/註冊 使用EventFakeEcpay帶去登入/註冊-->
              <EventFakeEcpay :cost="eventCard?.paymentTicket.price" :language="language"></EventFakeEcpay>
            </template>
            <template v-else>
              <template 
                v-if="(eventCard?.paymentTicket.ticketTypeID === 1 || eventCard?.paymentTicket.ticketTypeID === 3)">
                <EventEcpay :eventID="eventCard?.eventID" :cost="eventCard?.paymentTicket.price"
                  :ticketTypeID="eventCard?.paymentTicket.ticketTypeID" :isApply="eventCard?.isApply" :button-text=1
                  :button-type=1>></EventEcpay>
              </template>
              <template v-else><!--不是會員 先廣告再購票-->
                <EventADs :eventID="eventCard?.eventID" :cost="eventCard?.paymentTicket.price"
                  :ticketTypeID="eventCard?.paymentTicket.ticketTypeID" :isApply="eventCard?.isApply">
                </EventADs>
              </template>
            </template>
            <EventGoogleMap :storeName=eventCard?.storeName :location=eventCard?.location></EventGoogleMap>
            <EventShare :eventID="eventCard?.eventWebIndex ?? eventCard?.eventID" :language="language"></EventShare>
          </div>
          <div v-if="isEdit" class="button-container"><!--給品牌方使用者看的-->
            <!--<el-button :style="{ borderRadius: '8px', minWidth: '200px', maxWidth: '300px', fontSize: '16px', color: '#266cb5', fontWeight: 'bold' }"
                plain size="large" >{{ $t('brand.getRegistrationList') }} </el-button>
            <EventGoogleMap :storeName=eventCard?.storeName :location=eventCard?.location></EventGoogleMap>
            <EventShare :eventID="eventCard?.eventWebIndex ?? eventCard?.eventID" :language=language></EventShare>-->
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped>
.el-col {
  /*兩row之間的空格高度*/
  margin-bottom: 24px;
}

.column-spacing {
  /*column之間的空格寬度*/
  padding: 5px;
}

.card {
  border-radius: 10px;
  /*圓角 */
  overflow: hidden;
  cursor: pointer;
  /*顯示手指 */
  transition: transform 0.2s;
}

.card:hover {
  transform: scale(1.015);
  /* 放大效果 */
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-wrapper {
  width: 100%;
  aspect-ratio: 16 / 9;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative; /* 添加相對定位 */
}

.image {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}

.overlay {
  position: absolute; /* 絕對定位 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明背景 */
  color: white; /* 文字顏色 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5em; /* 文字大小 */
}

.content-wrapper {
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 16px;
  /* 調整字體大小 */
  line-height: 1.5;
  /* 增加行距 */
}

.bold {
  font-weight: bold;
  /* 設置粗體 */
}

.nameMenu-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.button-container {
  display: flex;
  gap: 5px;
  /* 按鈕之間的間距 */
  margin-top: 10px;
  /* 與上方組件的間距 */
}

.button-container :deep(.el-button + .el-button) {
  margin-left: 0 !important;
  /* 覆蓋 Element Plus 預設的 margin-left */
}
</style>
