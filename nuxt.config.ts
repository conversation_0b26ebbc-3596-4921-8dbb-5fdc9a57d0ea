import dotenv from "dotenv";
import { Configuration } from 'webpack';
dotenv.config();

export default {
  ssr: false,
  app: {
    head: {
      link: [
        {
          rel: "preconnect",
          href: "https://fonts.googleapis.com",
        },
        {
          rel: "preconnect",
          href: "https://fonts.gstatic.com",
        },
        {
          rel: "stylesheet",
          href: "https://fonts.googleapis.com/css2?family=Noto+Sans&display=swap",
        },
        { rel: "icon", type: "image/png", href: "/favicon.ico" },
      ],
      script: [
        {
          src: "https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js",
        },
      ],
    },
  },
  meta: {
    title: "iTalkuTalk",
    meta: [
      { charset: "utf-8" },
      { name: "viewport", content: "width=device-width, initial-scale=1" },
      {
        hid: "description",
        name: "description",
        content: "iTalkuTalk, learning 9 different languages",
      },
    ],
  },
  css: ["element-plus/dist/index.css", "~/assets/css/main.css"],
  components: ["~/components/"],
  plugins: [
    { src: "~/plugins/pinia.ts", mode: "client" },
    { src: "~/plugins/vue3-google-login.client.ts", mode: "client" },
    "~/plugins/i18n.ts",
    "~/plugins/vue-apple-login.js",
  ],
  buildModules: [
    [
      "@nuxtjs/google-fonts",
      {
        googleFonts: {
          families: {
            Roboto: true,
            "Josefin+Sans": true,
            Lato: [100, 300],
            Raleway: {
              wght: [100, 400],
              ital: [100],
            },
          },
        },
      },
    ],
    ["@nuxtjs/proxy"],
    ["@pinia/nuxt"],
    ["@nuxtjs/i18n"],
  ],
  runtimeConfig: {
    public: {
      axiosBaseUrl: process.env.API_URL,
      webBaseUrl: process.env.WEB_URL,
      fbClientId: process.env.FB_CLIENT_ID,
      fbClientSecret: process.env.FB_CLIENT_SECRET,
      appleClientId: process.env.APPLE_CLIENT_ID,
      googleClientId: process.env.GOOGLE_CLIENT_ID,
      version: process.env.VERSION,
      ecpayUrl: process.env.ECPAY_URL
    },
  },
  router: {
    // turns off prefetching (since the default is true)
    middleware: ["auth"],
  },
  build: {
    transpile: ['@aws-sdk/client-s3', '@aws-sdk/client-cognito-identity', '@aws-sdk/credential-provider-cognito-identity']
  },
  vite: {
    optimizeDeps: {
      include: ['@aws-sdk/client-s3', '@aws-sdk/client-cognito-identity', '@aws-sdk/credential-provider-cognito-identity']
    }
  }
};
