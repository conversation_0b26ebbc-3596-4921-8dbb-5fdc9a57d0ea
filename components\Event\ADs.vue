<template>
  <el-button :style="{ minWidth: '150px', fontSize: '16px', color: '#111111' }" type="info" plain size="large"
    @click=handleADsClick>{{ buttonText }} </el-button>
  <el-dialog :modelValue="isADsVisible" :width="'340px'" :before-close="handleClose" append-to-body>
    <div class="dialog-content">
      <el-button class="buyMember" :style="{ minWidth: '250px', fontSize: '16px', color: '#266cb5', fontWeight: 'bold' }" plain
        size="large" @click=handleToAccountClick>{{ $t('event.adsText') }} </el-button>
      <EventEcpay :eventID=props.eventID :cost=props.cost :ticketTypeID=props.ticketTypeID :isApply=props.isApply
        :buttonType=2 :buttonText=2>
      </EventEcpay>
    </div>
  </el-dialog>
<MainToApp :text=toAppText :visible=isDialogVisible @update:visible="isDialogVisible = $event" />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { useRoute } from 'vue-router';
const props = defineProps({
  eventID: {//活動ID
    type: String,
    required: true,
  },
  ticketTypeID: {//哪一種票價
    type: Number,
    required: true,
  },
  isApply: {//是否已購買
    type: Boolean,
    required: true,
  },
  cost: {//價格
    type: Number,
    required: true,
  }
});
const { t } = useI18n();
const isADsVisible = ref(false);//廣告
const isDialogVisible = ref(false);//導向APP
const googlePlayUrl = "https://play.google.com/store/apps/details?id=lab.italkutalk";
const appStoreUrl = "https://apps.apple.com/tw/app/italkutalk/id1263409577";
const toAppText = ref("event.useTicketInApp");
const buttonText = computed(() => {
  return props.isApply ? t('event.registered') : `${t('event.register')} TWD ${props.cost}$`;
});
const route = useRoute();
let language = ref(route.query.language || 'default');
async function handleADsClick(event: MouseEvent) {//點擊按鈕時 跳出廣告
  event.stopPropagation();
  if(props.isApply)
    isDialogVisible.value = true;//已購票
  else
    isADsVisible.value = true;//未購票
  console.log("點了")
};
function handleToAccountClick() {
  navigateTo('/account');
};

// 關閉 dialog 的方法
function handleClose(done: () => void) {
  isADsVisible.value = false;
  done();
};
watch(
  () => route.query.language,
  (newLanguage) => {
    language.value = newLanguage || 'default';
  }
);
</script>

<style scoped>
.buyMember {
  border-radius: 8px;
  /* 圓角 */
  margin-bottom: 10px;
  max-width: 300px;
  overflow: hidden;
  word-wrap: break-word; 
}

:deep(.el-button + .el-button) {
  margin-left: 0 !important;
  /* 取消 margin-left */
}
</style>