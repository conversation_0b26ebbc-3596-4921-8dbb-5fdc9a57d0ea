<template>
  <div v-if="brandDetail?.brandStatus === 0 && isEdit">
    <!--新增品牌時-->
    <el-card class="editBox-card">
      <div class="padding-div">
        <span class="formTitle">{{ $t('edit.newBrand') }}</span>
        <EditBrandEditForm :brandDetail="brandDetail" :brandID="props.brandID"></EditBrandEditForm>
      </div>
    </el-card>
  </div>
  <div v-if="brandDetail?.brandStatus === 1">

    <div class="main-div">
      <div class="">
        <div class="brandImage-wrapper">
          <img :src=brandDetail?.photo class="brandImage" alt="品牌封面照片" />
        </div>
        <div class="content-wrapper">
          <div class="introMenu-content">
            <div class="brandIntro-content">
              <el-avatar :style="{ minWidth: '72px' }" :size="72" :src=brandDetail?.icon></el-avatar>
              <div class="brandIntro-text">
                <div><span class="brandName">{{ brandDetail?.name }}</span></div>
                <div class="brandScore">
                  <span class="">{{ $t('brand.score') }}：{{ brandDetail?.rating }}</span>
                  <EventViewRating :brandID="brandDetail?.brandID"></EventViewRating>
                </div>
                <div><span class="">{{ $t('brand.eventCount') }}：{{ brandDetail?.eventCount }}</span></div>
              </div>
            </div>
            <EditBrandMenu v-if="isEdit" :brandDetail="brandDetail"></EditBrandMenu>
          </div>
          <div class="brandDetail-content">
            <div class="detailTitle">
              <span>{{ $t('brand.detail') }}</span>
              <EventTrans v-if="brandDetail?.oriLanguage != language" @update:isTrans="isTrans = $event"
                :text="'brand.detail'"></EventTrans>
            </div>
            <div v-if="isTrans" class="description-text trans-description" v-html="transDescriptionHtml"></div>
            <div v-if="!isTrans" class="description-text ori-description" v-html="oriDescriptionHtml"></div>
          </div>
          <div v-if="brandDetail?.youtubeID" class="iframe-container">
            <iframe :src="'https://www.youtube.com/embed/' + brandDetail.youtubeID" title="YouTube video player"
              frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
          </div>
        </div>
        <BrandTabs @update:selectTag="selectTag = $event"></BrandTabs>
        <div class="padding-div">
          <el-col v-if="brandDetail" :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-space :fill="true" style="width: 100%">
              <div v-if="selectTag === '-1'">
                <TeacherCard :brandID=brandDetail?.brandID :isEdit=props.isEdit />
              </div>
              <div v-if="selectTag === '0'">
                <EventCard :brandID=brandDetail?.brandID :isEdit=props.isEdit :isPast=false />
              </div>
              <div v-if="selectTag === '1'">
                <EventCard :brandID=brandDetail?.brandID :isEdit=props.isEdit :isPast=true />
              </div>
            </el-space>
          </el-col>
        </div>
      </div>
      <div class="right-div"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getBrandDetail } from "../../api/brand";
import { getGuestKey } from "../../api/user";
import { BrandDetail } from "~~/models/brand";
import { User } from "~~/models/user";
import { globalStore } from "../../stores/global";
import { useI18n } from "vue-i18n";
const { t } = useI18n()
const route = useRoute();
const store = globalStore();
const loading = ref(true);
const isTrans = ref(true);
const brandDetail = ref<BrandDetail>();
let user: User;
const language = ref(route.query.language || 'default');
const selectTag = ref('-1');
const props = defineProps({
  brandID: {//這邊傳進來有可能是brandID 有可能其實是brandWebIndex 要注意
    type: String,
    required: true,
  },
  isEdit: {//是否是編輯模式(從'我的品牌'進入)
    type: Boolean,
    required: true,
  }
});
onMounted(async () => {//進入畫面時
  await getDetail();//取得品牌詳情
  loading.value = false;
  watchEffect(() => {//寫在這裡而非index.vue 或是上面一點 是因為要等brandDetail有值之後才能call
    useHead({
      title: `${brandDetail.value?.name ?? ''} - ${t('home.title')}`,
    });
  });
});
async function getDetail() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    brandDetail.value = await getBrandDetail({ guestKey: guestKey, language: languageString, brandID: props.brandID });
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    brandDetail.value = await getBrandDetail({ brandID: props.brandID });
  }
}
function handleDoNotThing(event: MouseEvent) {//點擊卡片時
  event.stopPropagation();
}
watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  loading.value = true; // 設置加載狀態
  await getDetail(); // 重新獲取品牌詳情
  loading.value = false; // 加載完成
  console.log('切語言')
  console.log(brandDetail.value?.transDescription)
});

//#region 簡介中可以顯示出超連結
const transDescriptionHtml = ref('');
const oriDescriptionHtml = ref('');
const linkify = (text: string) => {
  const urlRegex = /https?:\/\/[^\s]+/g;
  return text.replace(urlRegex, (url) => {
    return `<a href="${url}" target="_blank" style="color: blue;">${url}</a>`;
  });
};

const updateDescriptions = () => {
  if (brandDetail.value) {
    if (brandDetail.value.transDescription) {
      transDescriptionHtml.value = linkify(brandDetail.value.transDescription);
    }
    if (brandDetail.value.oriDescription) {
      oriDescriptionHtml.value = linkify(brandDetail.value.oriDescription);
    }
  }
};

onMounted(() => {
  updateDescriptions();
});

watch(//切換語言時
  () => brandDetail.value?.transDescription,
  () => {
    updateDescriptions();
  }
);

watch(//切換語言時
  () => brandDetail.value?.oriDescription,
  () => {
    updateDescriptions();
  }
);
//#endregion
</script>

<style scoped>
.editBox-card {
  margin: 20px;
  max-width: 1000px;
}

.el-card {
  border-radius: 10px;
  /* 卡片的圓角 */
  overflow: hidden;
  /* 確保圓角效果顯示 */
  --el-card-padding: 0;
}

@media (max-width: 500px) {
  .editBox-card {
    margin: 0px;
  }

  .el-card {
    border-radius: 0px;
  }
}

.main-div {
  display: flex;
}

.right-div {
  min-width: 250px;
  background-color: #FFFFFF
}

.brandImage-wrapper {
  width: 100%;
  max-height: 20vh;
  /* 設置圖片的寬高比 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brandImage {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}

.content-wrapper {
  padding: 15px 25px 0px 25px;
  background-color: #FFFFFF;
  overflow-x: scroll;
}

.content-wrapper::-webkit-scrollbar {
  display: none;
}

@media (max-width: 500px) {
  .content-wrapper {
    padding: 10px 10px 0px 10px;
  }
}

.brandIntro-content {
  display: flex;
  align-items: center;
}

.introMenu-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.brandName {
  font-size: 24px;
}

.brandIntro-text {
  margin-left: 10px;
}

.brandScore {
  display: flex;
  align-items: baseline;
  gap: 10px;
}

.brandDetail-content {
  padding: 15px 0px 10px 0px;
  /*display: flex;
  flex-direction: column;
  flex-wrap: wrap;*/
  /*text-align: justify;*/
}

.detailTitle {
  font-size: 20px;
  margin-bottom: 10px;
  align-items: center;
  display: flex;
  gap: 10px;
}


.description-text {
  width: auto;
  white-space: pre-wrap;
}

.iframe-container {
  position: relative;
  width: 100%;
  /* 可以根據需要調整父容器的最大寬度 */

  /* 16:9 比例 */
  padding-top: 56.25%;
  overflow: hidden;
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.padding-div {
  padding: 20px;
  overflow-x: auto;
  /* 允許橫向滑動 */
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  /* 平滑滾動，適用於移動設備 */
}

.padding-div::-webkit-scrollbar {
  display: none;
  /* 隱藏滾動條 */
}

@media (max-width: 500px) {
  .padding-div {
    padding: 0px;
  }
}

@media (max-width: 1300px) {
  .right-div {
    min-width: 0px;
  }

}

@media (min-width: 1300px) {
  .detailTitle {
    font-size: 24px;
  }

  .description-text {
    font-size: 18px;
  }
}
</style>