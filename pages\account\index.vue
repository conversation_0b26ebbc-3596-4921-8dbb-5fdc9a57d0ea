<script setup lang="ts">
import { globalStore } from "../../stores/global";
import { ElMessageBox, ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router';
import { Member } from "models/user";
import { checkMember } from "../../api/user";

const router = useRouter();
const { t } = useI18n();
const route = useRoute();
let language = ref(route.query.language || 'default');
watch(
  () => route.query.language,
  (newLanguage) => {
    language.value = newLanguage || 'default';
  }
);
watchEffect(() => {
  useHead({
    title: `${t('menu.account')} - ${t('home.title')}`,
  });
});
const store = globalStore();
definePageMeta({
  middleware: ["auth"],
});
const navigateToTerms = () => {
  router.push({ path: '/terms' });
};
const iosnavigateToTerms = () => {
  router.push({ path: '/iosterms' });
};
const isMemberActive = computed(() => {
  if (!member.value || !member.value.memberInfo) {
    return false;
  }
  const memberEnd = new Date(member.value.memberInfo.memberEnd);
  const now = new Date();
  console.log(memberEnd);
  return memberEnd > now;
});
const member = ref<Member>();
onMounted(async () => {
  member.value = await checkMember();
  console.log(isMemberActive.value);
});
const handleSignOut = async () => {
  await store.signOutAndClearCache();
  // 因為login.vue中的sign in with apple套件在連續登入登出時，onSuccess/onFailure會重複觸發
  // 所以強制刷新一次頁面
  window.location.reload();
  navigateTo("/home");
};

</script>

<template>
  <div class="padding-div">
    <el-row :gutter="8">
      <el-col :sm="24" :md="16">
        <!--第一框顯示帳號資料-->
        <el-space :fill="true" style="width: 100%">
          <div class="user-info">
            <div class="user-header">
              <h2>{{ $t('account.info') }}</h2>
            </div>
            <div class="memberInfo" v-if="isMemberActive">
              <div>{{ $t("account.isMember") }}：</div>
              <div class="duration-bg">
                <text class="duration-text">
                  {{ $t("promote.member_duration") }} :
                  {{ new Date(member?.memberInfo?.memberStart ? member.memberInfo.memberStart : 0).toLocaleDateString()
                  }}
                  ~
                  {{ new Date(member?.memberInfo?.memberStart ? member.memberInfo.memberEnd : 0).toLocaleDateString() }}
                </text>
              </div>
            </div>
            <div class="user-content">
              <el-avatar :size="72" :src="store.getCurrentUser.userPhoto"></el-avatar>
              <div class="user-details">
                <h2 class="user-name">{{ store.getCurrentUser.userName }}</h2>
                <p class="user-email">{{ store.getCurrentUser.userEmail }}</p>
              </div>
            </div>
            <div class="termsAndLogout">
              <div class="user-terms">
                <el-link type="info" @click="navigateToTerms" target="_blank">{{ $t('account.terms') }}</el-link>
              </div>
              <div class="logout">
                <el-button type="info" plain @click="handleSignOut">{{ $t('menu.logout') }}</el-button>
              </div>
            </div>
          </div>
        </el-space>
        <el-space :fill="true" style="width: 100%">
          <div class="webADsinfo">
            <AccountMemberEcpay :isMember="member?.result" :language="language"></AccountMemberEcpay>
          </div>
        </el-space>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.user-info {
  padding: 24px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
}

.user-header {
  margin-bottom: 10px;
}

.user-content {
  display: flex;
  align-items: flex-start;
}

.user-avatar {
  margin-right: 10px;
}

.user-name {
  margin: 0;
  padding: 0;
  margin-left: 15px;
  margin-top: 5px;
}

.user-email {
  margin-top: 15px;
  margin-left: 15px;
}

.delete-account {
  padding: 16px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  margin-top: 15px;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-form {
  display: flex;
  flex-direction: column;
}

.el-card {
  min-width: 250px;
  padding: 20px;
}

.el-card__body {
  width: 100%;
  padding: 0;
}

.memberInfo {
  margin-bottom: 20px;
  line-height: 1.5;
  display: flex;
  align-items: center;
}

.duration-text {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  color: #468ee6;
}

.duration-bg {
  padding: 6px;
  background: #f4f8fa;
  border-radius: 5px;
}

.webADsinfo {
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.termsAndLogout {
  flex-direction: row;
  display: flex;
  align-items: baseline;
  gap: 20px;
}
.padding-div {
  padding: 20px;
}

@media (max-width: 500px) {
  .memberInfo {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 400px) {
  .user-info {
    margin-bottom: 5px;
  }
  .padding-div {
    padding: 0px;
  }
}
</style>
