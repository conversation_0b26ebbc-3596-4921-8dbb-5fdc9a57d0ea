<template>
  <el-dialog :modelValue="visible" :custom-class="dialogClass" :before-close="handleClose" append-to-body>
    <div v-if="type === 1">
      <span class="editGeneral-dialogTitle">{{ $t('edit.editBrand') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditBrandEditForm :brandID="props.brandDetail!.brandID" :brandDetail="props.brandDetail!"> </EditBrandEditForm>
    </div>
    <div v-if="type === 2">
      <span class="editGeneral-dialogTitle">{{ $t('brand.addTeacher') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditTeacherEditForm :brandID="props.brandDetail!.brandID" :isAdd=true></EditTeacherEditForm>
    </div>
    <div v-if="type === 3">
      <span class="editGeneral-dialogTitle">{{ $t('brand.addEvent') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditEventEditForm :brandID="props.brandDetail!.brandID" :isAdd="true" :isPast=false></EditEventEditForm>
    </div>
    <div v-if="type === 4">
      <span class="editGeneral-dialogTitle">{{ $t('brand.addPastEvent') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditEventEditForm :brandID="props.brandDetail!.brandID" :isAdd="true" :isPast=true></EditEventEditForm>
    </div>
    <div v-if="type === 5">
      <span class="editGeneral-dialogTitle">{{ $t('brand.editTeacher') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditTeacherEditForm :teacherID="props.teacherID" :isAdd="false" :isInside="props.isInside"></EditTeacherEditForm>
    </div>
    <div v-if="type === 6">
      <span class="editGeneral-dialogTitle">{{ $t('brand.deleteTeacher') }}</span>
      <div class="editGeneral-spacer"></div>
      <span class="editGeneral-text">{{ $t('brand.deleteTeacherText1') }}</span>
      <span class="editGeneral-text">{{ props.teacherName }}</span>
      <span class="editGeneral-text">{{ $t('brand.deleteTeacherText2') }}</span>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-button">
        <el-button :style="{ width: '150px' }" @click="handleClose">{{ $t('event.no') }}</el-button>
        <el-button :style="{ width: '150px' }" @click="handleOK" type="primary">{{ $t('event.yes') }}</el-button>
      </div>
    </div>
    <div v-if="type === 7">
      <span class="editGeneral-dialogTitle">{{ $t('brand.edit') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditEventEditForm :eventID="props.eventCard!.eventID" :isAdd="false" :onlyText="false" :isPast=false></EditEventEditForm>
    </div>
    <div v-if="type === 8">
      <span class="editGeneral-dialogTitle">{{ $t('brand.edit') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditEventEditForm :eventID="props.eventCard!.eventID" :isAdd="false" :onlyText="true" :isPast=false></EditEventEditForm>
    </div>
    <div v-if="type === 9">
      <span class="editGeneral-dialogTitle">{{ $t('brand.edit') }}</span>
      <div class="editGeneral-spacer"></div>
      <EditEventEditForm :eventID="props.eventCard!.eventID" :isAdd="false" :isPast=true></EditEventEditForm>
    </div>
    <div v-if="type === 10">
      <span class="editGeneral-dialogTitle">{{ $t('brand.getRegistrationList') }}</span>
      <div class="editGeneral-spacer"></div>
      <EventRegistrationList :eventCard="props.eventCard!"></EventRegistrationList>
    </div>
    <div v-if="type === 11">
      <span class="editGeneral-dialogTitle">{{ $t('brand.downEven') }}</span>
      <div class="editGeneral-spacer"></div>
      <span class="editGeneral-text">{{ $t('brand.downEventText1') }}</span>
      <span class="editGeneral-text">{{ props.eventCard!.eventName }}</span>
      <span class="editGeneral-text">{{ $t('brand.downEventText2') }}</span>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-button">
        <el-button :style="{ width: '150px' }" @click="handleClose">{{ $t('event.no') }}</el-button>
        <el-button :style="{ width: '150px' }" @click="handleOK" type="primary">{{ $t('event.yes') }}</el-button>
      </div>
    </div>
    <div v-if="type === 12">
      <span class="editGeneral-dialogTitle">{{ $t('brand.upEvent') }}</span>
      <div class="editGeneral-spacer"></div>
      <span class="editGeneral-text">{{ $t('brand.upEventText1') }}</span>
      <span class="editGeneral-text">{{ props.eventCard!.eventName }}</span>
      <span class="editGeneral-text">{{ $t('brand.upEventText2') }}</span>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-button">
        <el-button :style="{ width: '150px' }" @click="handleClose">{{ $t('event.no') }}</el-button>
        <el-button :style="{ width: '150px' }" @click="handleOK" type="primary">{{ $t('event.yes') }}</el-button>
      </div>
    </div>
    <div v-if="type === 13">
      <span class="editGeneral-dialogTitle">{{ $t('brand.deleteEvent') }}</span>
      <div class="editGeneral-spacer"></div>
      <span class="editGeneral-text">{{ $t('brand.deleteEventText1') }}</span>
      <span class="editGeneral-text">{{ props.eventCard!.eventName }}</span>
      <span class="editGeneral-text">{{ $t('brand.deleteEventText2') }}</span>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-spacer"></div>
      <div class="editGeneral-button">
        <el-button :style="{ width: '150px' }" @click="handleClose">{{ $t('event.no') }}</el-button>
        <el-button :style="{ width: '150px' }" @click="handleOK" type="primary">{{ $t('event.yes') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import { BrandDetail } from "~~/models/brand";
import { EventCard } from "~~/models/event";
import { deleteTeacher } from "~~/api/teacher";
import { removeEvent, deleteEvent } from "~~/api/event";
const { t } = useI18n();
const props = defineProps({
  type: {
    type: Number,
    required: true // 1 編輯品牌 2 新增教師 3 新增活動 4 新增過往活動 (從'我的品牌'進去 BrandMenu)
                   // 5 編輯教師 6 刪除教師 (TeacherMenu)
                   // 7 編輯活動(全) 8 編輯活動(僅文字) 9 編輯過往活動 10 報名狀況
                   // 11 下架活動 12 重新上架 13 刪除活動
  },
  visible: {
    type: Boolean,
    required: true // 是否可見
  },
  brandDetail: {
    type: Object as PropType<BrandDetail>, // 品牌詳細信息
  },
  teacherID: {
    type: String,
    default: '' // 教師ID
  },
  teacherName: {
    type: String,
    default: '' // 教師名字
  },
  isInside: {
    type: Boolean,
    default: false // 是否在教師編輯頁面內編輯
  },
  eventCard: {
    type: Object as PropType<EventCard>,// 活動卡片
  }
});
const emit = defineEmits(['update:visible']);//返回父組件
// 關閉 dialog 的方法
function handleClose(done: () => void) {
  emit('update:visible', false);
  done();
};
async function handleOK() {
  let status = 0
  switch (props.type) {
    case 6:
      status = await deleteTeacher(props.teacherID);
      break;
    case 11:
      status = await removeEvent(props.eventCard!.eventID, true);
      break;
    case 12:
      status = await removeEvent(props.eventCard!.eventID, false);
      break;
    case 13:
      status = await deleteEvent(props.eventCard!.eventID);
      break;
  }
  if (!status)
    window.location.reload();
  emit('update:visible', false);
};

const dialogClass = computed(() => {//有些大表單寬度要比較寬 有些只是確認功能的 寬度窄一點
  switch (props.type) {
    case 6:
    case 11:
    case 12:
    case 13:
      return 'editGeneral-dialog width400';
    case 10:
      return 'editGeneral-dialog width500';
    default:
      return 'editGeneral-dialog';
  }
});
</script>

<style>
.editGeneral-dialog {
  max-width: 700px;
  width: 100%;
  margin-top: 5vh;
}

.editGeneral-dialog.width500 {
  max-width: 500px;
  width: 100%;
  margin-top: 5vh;
}

.editGeneral-dialog.width400 {
  max-width: 400px;
  width: 100%;
  margin-top: 25vh;
}


.editGeneral-dialog :deep(.el-dialog + .el-dialog) {
  --el-dialog-width: 100% !important;
  /* 覆蓋 Element Plus 預設的 max-width */
}

.editGeneral-spacer {
  height: 10px;
  /* 調整這裡來模擬 gap */
}

.editGeneral-dialogTitle {
  font-size: 20px;
  font-weight: bolder;
}

.editGeneral-text {
  font-size: 16px;
}

.editGeneral-button {
  display: flex;
  justify-content: space-around;
}
</style>