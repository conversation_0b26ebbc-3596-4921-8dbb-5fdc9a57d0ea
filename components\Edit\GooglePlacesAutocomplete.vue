<template>
  <!-- 使用Element Plus的AutoComplete組件 -->
  <el-autocomplete 
    v-model="placeQuery" 
    :fetch-suggestions="handleSearch" 
    :style="{  width: '260px' }"
    :placeholder="$t('edit.useLocationSearch')"
    @select="handleSelect">
    <template #default="{ item }">
      <div>{{ item.label }}</div>
    </template>
  </el-autocomplete>
</template>

<script setup>
import { ref, onMounted, defineProps, defineEmits, watch } from 'vue';
import { countryCodeToLanguageCode } from '@/util/language';

const props = defineProps(['modelValue']);
const emit = defineEmits(['update:modelValue', 'place-selected']);

const placeQuery = ref(props.modelValue);
const placeOptions = ref([]);
const selectedPlace = ref(null);

watch(placeQuery, (newVal) => {
  emit('update:modelValue', newVal);
});

let autocompleteService = null;
let placesService = null;

const loadGoogleMapsScript = (callback) => {
  const existingScript = document.getElementById('google-maps');
  if (!existingScript) {
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyDGLhaS6p3riUiXVlob68azEfQ2QOVlgP0&libraries=places`;
    script.id = 'google-maps';
    document.body.appendChild(script);
    script.onload = () => {
      if (callback) callback();
    };
  } else if (callback) {
    callback();
  }
};

const initializeGoogleMaps = () => {
  if (window.google) {
    autocompleteService = new google.maps.places.AutocompleteService();
    placesService = new google.maps.places.PlacesService(document.createElement('div'));
  }
};

// 處理搜索請求
const handleSearch = (queryString, cb) => {
  if (!queryString) {
    cb([]);
    return;
  }

  if (autocompleteService) {
    autocompleteService.getPlacePredictions({ input: queryString, language: 'zh-TW' }, (predictions, status) => {
      if (status !== google.maps.places.PlacesServiceStatus.OK || !predictions) {
        cb([]);
        return;
      }

      const results = predictions.map((prediction) => ({
        value: prediction.place_id,
        label: prediction.description
      }));

      placeOptions.value = results;
      cb(results);
    });
  }
};

// 處理選擇建議
const handleSelect = (item) => {
  placeQuery.value = item.label;
  selectPlace(item.value);
};

// 選擇地點後處理
const selectPlace = (placeId) => {
  if (placesService) {
    placesService.getDetails({ placeId, language: 'en' }, (place, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK) {
        const countryComponent = place.address_components.find((comp) => comp.types.includes('country'));
        const countryCode = countryComponent ? countryComponent.short_name : 'TW';
        const language = countryCodeToLanguageCode[countryCode] || 'en';

        placesService.getDetails({ placeId, language }, (localizedPlace, localizedStatus) => {
          if (localizedStatus === google.maps.places.PlacesServiceStatus.OK) {
            selectedPlace.value = {
              storeName: localizedPlace.name,
              address: localizedPlace.formatted_address,
              location: { latitude: localizedPlace.geometry.location.lat(), longitude: localizedPlace.geometry.location.lng() },
              countryCode: countryCode
            };

            emit('place-selected', selectedPlace.value);
          }
        });
      }
    });
  }
};

onMounted(() => {
  loadGoogleMapsScript(initializeGoogleMaps);
});
</script>