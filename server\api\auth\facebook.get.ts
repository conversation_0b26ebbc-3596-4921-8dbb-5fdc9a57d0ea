import axios from "axios";

const config = useRuntimeConfig();
export default defineEventHandler(async (event) => {
  // 流程可以參考FB文件: https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow/

  const code = getQuery(event).code;

  // 透過code交換access_token
  console.log(config.public);

  const req = axios.get("https://graph.facebook.com/v17.0/oauth/access_token", {
    params: {
      client_id: config.public.fbClientId,
      client_secret: config.public.fbClientSecret,
      redirect_uri: `${config.public.webBaseUrl}/login/facebook`,
      code: code,
    },
  });

  const token = (await req).data.access_token;
  // 取得FB用戶資訊
  return (
    await axios.get("https://graph.facebook.com/me", {
      params: { access_token: token },
    })
  ).data;
});
