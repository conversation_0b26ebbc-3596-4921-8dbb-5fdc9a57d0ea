{"private": true, "scripts": {"dev": "nuxi dev --port 3000", "build": "nuxi build", "start": "node .output/server/index.mjs"}, "devDependencies": {"@intlify/nuxt3": "^0.1.10", "@nuxtjs/google-fonts": "^1.3.0", "@nuxtjs/svg": "^0.4.0", "@types/uuid": "^10.0.0", "nuxt": "^3.3.2"}, "dependencies": {"@aws-sdk/client-cognito-identity": "^3.609.0", "@aws-sdk/client-s3": "^3.609.0", "@aws-sdk/credential-provider-cognito-identity": "^3.609.0", "@nuxt/types": "^2.16.3", "@nuxtjs/composition-api": "^0.33.1", "@nuxtjs/i18n": "^7.3.1", "@nuxtjs/proxy": "^2.1.0", "@pinia/nuxt": "^0.1.8", "@types/jsonwebtoken": "^9.0.2", "axios": "^0.26.0", "browser-image-compression": "^2.0.2", "dotenv": "^16.0.3", "element-plus": "^2.0.2", "lodash": "^4.17.21", "pinia": "^2.0.12", "swiper": "^8.1.4", "uuid": "^10.0.0", "vue-apple-login": "2.x", "vue-i18n": "^9.2.2", "vue3-google-login": "^2.0.34"}}