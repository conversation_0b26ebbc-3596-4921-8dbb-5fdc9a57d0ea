<template>
  <el-dialog :title="$t('event.RegInfoTitle')" :modelValue="isEmailDialogVisible" custom-class="fillEmail-dialog"
    :before-close="handleClose" append-to-body>
    <div :style="{ marginBottom: '15px', fontSize: '16px', color: '#111111' }">{{ $t('event.regInfo1') }}</div>
    <el-form :model="infoForm">
      <el-form-item :style="{ fontSize: '16px', color: '#111111' }" label="Email">
        <el-input v-model="infoForm.email">
          <template #append>
            <el-button :icon="Close" @click="handleClear(0)"/>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item :style="{ fontSize: '16px', color: '#111111' }" :label="$t('event.name')">
        <el-input v-model="infoForm.name">
          <template #append>
            <el-button :icon="Close" @click="handleClear(1)"/>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div>{{ $t('event.regInfo2') }}</div>
    <div>Email：<EMAIL></div>
    <div :style="{ marginBottom: '15px' }">{{ $t('event.phone') }}</div>
    <span slot="footer" :style="{ justifyContent: 'center', display: 'flex' }">
      <el-button :style="{ borderRadius: '4px', fontSize: '16px', minWidth: '100px' }" @click="handleClose">{{
    $t('event.no')
  }}</el-button>
      <el-button :style="{ borderRadius: '4px', fontSize: '16px', minWidth: '100px' }" type="primary"
        @click="handleEcpayClick">{{ $t('event.yes') }}</el-button>
    </span>
  </el-dialog>
  <template v-if="props.buttonType === 1">
    <el-button :style="{ borderRadius: '8px', minWidth: '150px', fontSize: '16px', color: '#111111' }" type="info" plain
      size="large" @click=handleFirstClick>{{ buttonText }} </el-button>
  </template>
  <template v-else>
    <el-button
      :style="{ borderRadius: '8px', minWidth: '250px', maxWidth: '300px', fontSize: '16px', color: '#266cb5', fontWeight: 'bold' }"
      plain size="large" @click=handleFirstClick>{{ buttonText }} </el-button>
  </template>
  <MainToApp :text=toAppText :visible=isDialogVisible @update:visible="isDialogVisible = $event" />
</template>

<script setup lang="ts">
import { eventPaymentCreate } from "../../api/event";
import { PaymentCreateResult } from "~~/models/event";
import { useI18n } from 'vue-i18n';
import { globalStore } from "../../stores/global";
import { Close } from '@element-plus/icons-vue'
const props = defineProps({
  eventID: {//活動ID
    type: String,
    required: true,
  },
  ticketTypeID: {//哪一種票價
    type: Number,
    required: true,
  },
  isApply: {//是否已購買
    type: Boolean,
    required: true,
  },
  cost: {//價格
    type: Number,
    required: true,
  },
  buttonText: {//文字
    type: Number,
    required: true,
  },
  buttonType: {//按鈕樣式
    type: Number,
    required: true,
  }
});
const store = globalStore();
const { t } = useI18n();
const paymentCreateResult = ref<PaymentCreateResult>();
const isDialogVisible = ref(false);
const isEmailDialogVisible = ref(false);
const toAppText = ref("event.useTicketInApp");
const config = useRuntimeConfig();
const infoForm = ref({
  email: store.getCurrentUser.userEmail,
  name: store.getCurrentUser.userName
});
const buttonText = computed(() => {
  if (props.buttonText === 1)
    return props.isApply ? t('event.registered') : `${t('event.register')} TWD ${props.cost}$`;
  if (props.buttonText === 2)
    return props.isApply ? t('event.registered') : `${t('event.directRegister')}(TWD ${props.cost}$)`;
});
function handleFirstClick(event: MouseEvent) {//點擊電子票券按鈕 導向APP 點擊報名按鈕 跳出填資料視窗
  event.stopPropagation();
  if (props.isApply === true) {
    console.log("已經買過 導向APP")
    isDialogVisible.value = true;
  } else {
    isEmailDialogVisible.value = true;
  }
}
function handleClose(event: MouseEvent) {//關閉填資料視窗
  isEmailDialogVisible.value = false;
}

function handleClear(row: number) {//清空該欄位資料
  if(row)
    infoForm.value.name = '';
  else
    infoForm.value.email = ''; 
}



async function handleEcpayClick() {//點擊確認資料按鈕時
  console.log(infoForm.value);
  if (infoForm.value.email === "" || infoForm.value.name === "")
    return;//將資料送出 如果有錯誤就跳返回

  paymentCreateResult.value = await eventPaymentCreate(props.eventID, props.ticketTypeID, infoForm.value.email, infoForm.value.name);
  console.log(paymentCreateResult.value);
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = `${config.public.ecpayUrl}`;
  //form.action = 'https://payment-stage.ecpay.com.tw/Cashier/AioCheckOut/V5';
  form.style.display = 'none';
  form.target = '_blank'; // 在新視窗中打開
  // 將 paymentCreateResult 的值動態添加到表單中
  let fields = [
    { name: 'MerchantID', value: paymentCreateResult.value.MerchantID },
    { name: 'MerchantTradeNo', value: paymentCreateResult.value.MerchantTradeNo },
    { name: 'MerchantTradeDate', value: paymentCreateResult.value.MerchantTradeDate },
    { name: 'PaymentType', value: paymentCreateResult.value.PaymentType },
    { name: 'TotalAmount', value: paymentCreateResult.value.TotalAmount.toString() },
    { name: 'TradeDesc', value: paymentCreateResult.value.TradeDesc },
    { name: 'ItemName', value: paymentCreateResult.value.ItemName },
    { name: 'ReturnURL', value: paymentCreateResult.value.ReturnURL },
    { name: 'ChoosePayment', value: paymentCreateResult.value.ChoosePayment },
    { name: 'CheckMacValue', value: paymentCreateResult.value.CheckMacValue },
    { name: 'EncryptType', value: paymentCreateResult.value.EncryptType.toString() },
    { name: 'NeedExtraPaidInfo', value: paymentCreateResult.value.NeedExtraPaidInfo },
  ];
  if (paymentCreateResult.value.Language) {
    fields.push({ name: 'Language', value: paymentCreateResult.value.Language });
  }
  fields.forEach(field => {
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = field.name;
    input.value = field.value;
    form.appendChild(input);
  });
  document.body.appendChild(form);
  form.submit();
  document.body.removeChild(form);
  isEmailDialogVisible.value = false;
};
</script>

<style>
.fillEmail-dialog {
  max-width: 500px;
  width: 100%;
}

.fillEmail-dialog :deep(.el-dialog + .el-dialog) {
  --el-dialog-width: 100% !important;
  /* 覆蓋 Element Plus 預設的 max-width */
}
</style>