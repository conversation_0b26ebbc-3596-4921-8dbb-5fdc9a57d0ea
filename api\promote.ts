import { PromoteImage, PromoteTeamResult, PromotePersonalResult } from "../models/promote";
import axios from "./axios";

export const getPromoteImage = async (
  userID: String
): Promise<[PromoteImage]> => {
  return (await axios.get(`/api/promote/images/${userID}`)).data.result as [
    PromoteImage
  ];
};

export const getPromotePersonalInfo = async (): Promise<PromotePersonalResult> => {//新方法 個人收益
  return (await axios.post("/api/promote/personal/info", {})).data.result as PromotePersonalResult;
};

export const getPromoteTeamInfo = async (): Promise<PromoteTeamResult> => {//新方法 團隊收益
  return (await axios.post("/api/promote/group/info", {})).data.result as PromoteTeamResult;
};