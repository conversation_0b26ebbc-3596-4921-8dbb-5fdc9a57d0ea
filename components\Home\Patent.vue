<script setup lang="ts">
definePageMeta({
  middleware: ["unauth"],
});
</script>

<template>
  <div class="appIntro-page">
    <!-- APP 下載卡片 -->
    <el-card class="appDownload-card">
      <div class="appDownload-content">
        <div class="text-content">
          <span class="patentA1">{{ $t('home.patentA1') }}</span>
          <span>1) Augmented reality interactive system and dynamic information interactive display method thereof,
            US20170053444A1 </span>
          <span>2) Image restoration method and image processing apparatus using the same, ********* B2</span>
          <span>3) Method for improving image quality, ********* B2</span>
          <span>4) Method and system for image haze removal based on hybrid dark channel prior, ********* B2</span>
          <span>5) Motion detection method based on grey relational analysis, ********* B1</span>
          <span>6) Method and apparatus for moving object detection using principal component analysis based radial
            basis function network, ********* B2</span>
          <span>7) Method and apparatus for moving object detection using fisher's linear discriminant based radial
            basis function network, US9286690 B2</span>
          <span>8) Method and image processing apparatus for image visibility restoration using fisher's linear
            discriminant based dual dark channel prior, US9305242 B2</span>
          <span>9) Method and system for vehicle identification, US9208172 B2</span>
          <span>10) Image processing method and image processing apparatus using the same, US9202116 B2</span>
          <span>11) Face annotation method and a face annotation system, US9195912 B1</span>
          <span>12) Face annotation method and face annotation system, US9183464 B1</span>
          <span>13) Method and image processing apparatus for image visibility restoration, US9177363 B1</span>
          <span>14) Probabilistic neural network based moving object detection method and an apparatus using the same,
            US9159137 B2</span>
          <span>15) Probabilistic neural network based moving object detection method and an apparatus using the same,
            US20150104062A1</span>
          <span>16) Method and apparatus for moving object detection based on cerebellar model articulation controller
            network, US9123133 B1</span>
          <span>17) Method for solving carpool matching problem and carpool server using the same, US9074904 B1</span>
          <span>18) High-performance block-matching VLSI architecture with low memory bandwidth for power-efficient
            multimedia devices, US8787461 B2</span>
          <span>19) Video decoding device, US8737471 B2</span>
          <span>20) Image processing apparatus, US20130287299A1 </span>
          <span>21) Image restoration method and image processing apparatus using the same, TW201612851A</span>
          <span>22) Method and image processing apparatus for image visibility restoration, TWI509567B</span>
          <span>23)
            Method and system for image haze removal based on hybrid dark channel prior, TWI514323B</span>
          <span>24)
            Face annotation method and face annotation system, TWI508002B</span>
          <span>25) Method and image processing
            apparatus for image visibility restoration using fisher's linear discriminant based dual dark channel prior,
            TWI501194B</span>
          <span>26) Method and apparatus for moving object detection based on cerebellar model
            articulation controller network, TW201537516A</span>
          <span>27) Method and apparatus for moving object
            detection, TWI512685B</span>
          <span>28) Method of improving image quality for display device,
            TW201308997A</span>
          <span>29) Method and apparatus for moving object detection based on cerebellar model
            articulation controller network, TW201537516A</span>
          <span>30) Motion detection method for complex scenes,
            TW201308254A</span>

        </div>
        <div class="image-content">
          <a target="_blank">
            <img src="https://itut-website.s3.ap-northeast-1.amazonaws.com/public/patent.jpg" alt="patent" class="patent" />
          </a>
        </div>
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.appIntro-page {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0px 20px 0px 20px;
}

.appDownload-card {
  padding: 20px;
  background: linear-gradient(to bottom, #ffffff, #daecf9);
  /* 背景漸層 */
}

.appDownload-content {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px;
}

.text-content {
  flex: 1;
  min-width: 150px;
  display: flex;
  flex-direction: column;
  color: #27465e;
  /* 文字內文顏色 */
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.1;
  /* 行距 */
  font-size: 14px;
  /* 字體大小 */
  margin-left: 25px;
}

.patentA1 {
  color: #27465e;
  /* 文字內文顏色 */
  font-weight: bold;
  /* 粗體 */
  font-family: Arial, sans-serif;
  /* 字體 */
  line-height: 1.5;
  /* 行距 */
  font-size: 22px;
}

.image-content {
  max-width: 360px;
  max-height: 535px;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.patent {
  width: auto;
  /* 自動調整寬度 */
  height: auto;
  /* 自動調整高度 */
  max-width: 100%;
  /* 最大寬度不超過父元件 */
  max-height: 535px;
  /* 最大高度不超過父元件 */
  object-fit: contain;
  /* 保持比例並確保圖片在容器內完整顯示 */
}

@media (max-width: 500px) {
  .image-content {
    justify-content: center;
    text-align: center;
  }

  .appIntro-page {
    padding: 0px;
  }
}

@media (max-width: 800px) {
  .appDownload-content {
    flex-direction: column;
  }

  .image-content {
    justify-content: center;
    text-align: center;
  }

  .text-content {

    margin-left: 0px;
  }
}

@media (min-width: 1500px) {
  .appIntro-page {
    padding-right: 100px;
  }
}

@media (min-width: 1600px) {
  .appIntro-page {
    padding-right: 200px;
  }
}

@media (min-width: 1700px) {
  .appIntro-page {
    padding-right: 300px;
  }
}
</style>