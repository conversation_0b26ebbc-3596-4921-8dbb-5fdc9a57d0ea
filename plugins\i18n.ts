import { createI18n } from "vue-i18n";
import de from "../locales/de.json";
import { defineNuxtPlugin } from "#app";
import en from "../locales/en.json";
import es from "../locales/es.json";
import fr from "../locales/fr.json";
import ja from "../locales/ja.json";
import ko from "../locales/ko.json";
import ru from "../locales/ru.json";
import vi from "../locales/vi.json";
import zh from "../locales/zh.json";

export default defineNuxtPlugin((nuxtApp) => {
  const i18n = createI18n({
    locale: "zh",
    fallbackLocale: "en",
    legacy: false,
    messages: {
      de: de,
      en: en,
      es: es,
      fr: fr,
      ja: ja,
      ko: ko,
      ru: ru,
      vi: vi,
      zh: zh,
    },
  });
  nuxtApp.vueApp.use(i18n);
});
