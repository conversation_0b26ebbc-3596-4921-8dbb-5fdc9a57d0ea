FROM node:16.16-alpine3.16

RUN mkdir -p /app
WORKDIR /app
COPY package.json /app
# COPY package-lock.json /app
COPY yarn.lock /app
RUN yarn

ENV NODE_ENV=production
ENV HOST 0.0.0.0

ARG VERSION
ENV VERSION=$VERSION

ARG API_URL
ENV API_URL=$API_URL

ARG WEB_URL
ENV WEB_URL=$WEB_URL

ARG FB_CLIENT_ID
ENV FB_CLIENT_ID=$FB_CLIENT_ID

ARG FB_CLIENT_SECRET
ENV FB_CLIENT_SECRET=$FB_CLIENT_SECRET

ARG APPLE_CLIENT_ID
ENV APPLE_CLIENT_ID=$APPLE_CLIENT_ID

ARG ECPAY_URL
ENV ECPAY_URL=$ECPAY_URL

ARG GOOGLE_CLIENT_ID
ENV GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID

COPY . /app

RUN yarn build

EXPOSE 3000

CMD [ "yarn", "start"]