import { Teacher<PERSON><PERSON>, TeacherDetail, Teacher<PERSON><PERSON><PERSON>, TeacherEdit } from "../models/teacher";
import axios from "./axios";
interface teacherOptions {//活動相關的API很多有不同Call法，用這樣的方法避免一直重寫export const
  brandID?: string//品牌ID拿來查教師列表
  teacherID?: string//教師ID拿來查教師詳情
  guestKey?: string;//未登入用戶需要用guestKey
  language?: string;//未登入用戶需要提供語言
}

export const getTeacherCard = async (options: teacherOptions = {}): Promise<TeacherCard[]> => {//老師列表
  const { guestKey, language, brandID } = options;
  const payload: any = {};
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;
  if (brandID) payload.brandID = brandID;

  return (await axios.post("/api/brand/teacher/card", payload)).data.result as TeacherCard[];
};

export const getTeacherDetail = async (options: teacherOptions = {}): Promise<TeacherDetail> => {//老師詳情
  const { guestKey, language, teacherID } = options;
  const payload: any = {};
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;
  if (teacherID) payload.teacherID = teacherID;
  if (teacherID) payload.teacherWebIndex = teacherID;

  return (await axios.post("/api/brand/teacher/detail", payload)).data.result as TeacherDetail;
};

export const checkTeacher = async (teacherID: string, teacherWebIndex: string): Promise<TeacherCheck> => {//middleWare檢查編輯教師權限
  return (await axios.post("/api/brand/teacher/check", { teacherID: teacherID, teacherWebIndex: teacherWebIndex })).data.result as TeacherCheck;
}

export const addTeacher = async (teacherAdd: TeacherEdit): Promise<number> => {//新增教師
  const { brandID, editingLanguage, name, description, nationality, primaryLanguage, icon, photo, teacherWebIndex, youtubeID } = teacherAdd;
  const response = await axios.post("/api/brand/owner/teacher/add", {
    brandID, editingLanguage, name, description, nationality, primaryLanguage, icon, photo, teacherWebIndex, youtubeID
  });
  return response.data.status as number;
}

export const editTeacher = async (teacherEdit: TeacherEdit): Promise<number> => {//編輯教師
  const { teacherID, editingLanguage, name, description, nationality, primaryLanguage, icon, photo, teacherWebIndex, youtubeID } = teacherEdit;
  const response = await axios.post("/api/brand/owner/teacher/edit", {
    teacherID, editingLanguage, name, description, nationality, primaryLanguage, icon, photo, teacherWebIndex, youtubeID
  });
  return response.data.status as number;
}

export const deleteTeacher = async (teacherID: string) => {//刪除教師
  return (await axios.post("/api/brand/owner/teacher/remove", { teacherID: teacherID })).data.result;
}