export interface BrandCard {//品牌列表
    brandID: string;
    photo: string;
    name: string;
    rating: number;
    brandWebIndex: string;
}
export interface BrandDetail {//品牌詳情
    brandID: string;
    photo: string;
    icon: string;
    name: string;
    rating: number;
    brandWebIndex: string;
    eventCount: number;
    transDescription: string;
    oriDescription: string;
    oriLanguage: string;
    youtubeID: string;
    brandStatus: number;
}

export interface BrandCheck {//品牌身分驗證-品牌
    brandID: string;
    brandWebIndex: string;
}

export interface BrandEdit {
    brandID: string; 
    editingLanguage: string; 
    name: string; 
    description: string; 
    icon: string; 
    photo: string; 
    brandWebIndex: string; 
    youtubeID: string;
}