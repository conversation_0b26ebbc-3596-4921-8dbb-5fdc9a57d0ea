<script setup lang="ts">
import { PromoteTeamResult, PromotePersonalResult } from "~~/models/promote";
import { getPromotePersonalInfo, getPromoteTeamInfo } from "../../api/promote";
import { globalStore } from "../../stores/global";
const store = globalStore();
let loading = ref(true);
const promotePersonalInfo = ref<PromotePersonalResult>();//新版個人推廣
const promoteTeamInfo = ref<PromoteTeamResult>();//新版推廣團隊
const level = ref(1);
onMounted(async () => {
  loading.value = false;
  promotePersonalInfo.value = await getPromotePersonalInfo();
  promoteTeamInfo.value = await getPromoteTeamInfo();
  level.value = promotePersonalInfo.value.memberInfo?.level; 
});
definePageMeta({
  middleware: ["auth", "member"],
});
</script>
<template>
  <el-row :gutter="8">
    <el-col :sm="24" :md="16">
      <el-space :fill="true" style="width: 100%">
        <PromoteProfile :userPhoto="store.getCurrentUser.userPhoto" :memberInfo="promotePersonalInfo?.memberInfo" />
        <PromoteEarning class="mobile_view" :earning="promotePersonalInfo?.bonus" :quantity="promotePersonalInfo?.quantityDetail" />
        <PromoteEarning v-if="level>1" class="mobile_view" :earning="promoteTeamInfo?.bonus" :quantity="promoteTeamInfo?.quantityDetail"
          cardTitle="promote.team_award_sum" cardContent="promote.current_team_promotion_count" />
        <PromoteDetail :detail="promotePersonalInfo?.promoteDetail"/>
        <PromoteTeamDetail v-if="level>1" :detail="promoteTeamInfo"/>
        <PromoteInfo :memberInfo="promotePersonalInfo?.memberInfo" :Level="level" />
        
      </el-space>
    </el-col>
    <el-col :span="8">
      <el-space :fill="true" style="width: 100%">
        <PromoteEarning class="desktop_view" :earning="promotePersonalInfo?.bonus" :quantity="promotePersonalInfo?.quantityDetail" />
        <PromoteEarning v-if="level>1" class="desktop_view" :earning="promoteTeamInfo?.bonus" :quantity="promoteTeamInfo?.quantityDetail"
          cardTitle="promote.team_award_sum" cardContent="promote.current_team_promotion_count" />
        
      </el-space>
    </el-col>
  </el-row>
</template>

<style>
.duration-bg {
  padding: 6px;
  background: #f4f8fa;
  border-radius: 5px;
}

.duration-text {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  color: #468ee6;
}

.img-content {
  width: 90px;
}

.description {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 23px;
  color: #4d4d4d;
}

.earning-text {
  font-style: normal;
  font-weight: bold;
  font-size: 40px;
  color: #406682;
}

.statistic-description {
  font-style: normal;
  font-size: 16px;
  color: #406682;
}

.statistic-count {
  font-style: normal;
  font-weight: bold;
  font-size: 32px;
  color: #406682;
}

.statistic-month {
  font-style: normal;
  font-size: 16px;
  color: #7a7a7a;
}
</style>
