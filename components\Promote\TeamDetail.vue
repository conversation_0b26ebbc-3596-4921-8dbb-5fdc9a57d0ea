<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import { PromoteTeamResult } from "~~/models/promote";
const props = defineProps({
  detail: { type: Object as () => PromoteTeamResult, default: null },
  cardTitle: { type: String, default: "promote.teamDetail" },
});
const selectedValue = ref('1');
const options = [
  {
    value: '1',
    label: 'promote.allTime',
  },
  {
    value: '2',
    label: 'promote.pastYear',
  },
  {
    value: '3',
    label: 'promote.pastMonth',
  }
]
const { t } = useI18n();
const translatedOptions = computed(() => {
  return options.map(option => ({
    value: option.value,
    label: t(option.label), // 調用i18n文字到選項中
  }));
});
</script>

<template>
  <el-card shadow="always">
    <div>{{ $t(cardTitle) }}</div>
    <el-skeleton v-if="detail == null" :rows="5" animated />
    <el-space v-else :fill="true" style="width: 100%" :size="16">
      <div>
        <el-select v-model="selectedValue" class="m-2" placeholder="Select" style="width: 120px">
          <el-option v-for="option in translatedOptions" :key="option.value" :label="option.label"
            :value="option.value" />
        </el-select>
      </div>
      <div v-if="selectedValue == '1'" class="table-responsive">
        <PromoteTeamTable :detail="detail.allTimeDetail" />
      </div>
      <div v-if="selectedValue == '2'" class="table-responsive">
        <PromoteTeamTable :detail="detail.pastYearDetail" />
      </div>
      <div v-if="selectedValue == '3'" class="table-responsive">
        <PromoteTeamTable :detail="detail.pastMonthDetail" />
      </div>
    </el-space>
  </el-card>
</template>
<style scoped>
.table-responsive {
  overflow-x: auto;
}

.custom-select-width {
  width: 100px !important;
}
</style>