# iTalkuTalk Web Nuxt3

## 設計文件

[Figma Design](https://www.figma.com/file/GDAQslFdCHBSehDuJTw5go/italkutalk-UI--owner?node-id=6088%3A19097)

## Prerequisite

- Nodejs 16 LTS

## Environment Variable

| Variable           | Description                    | Example                                                                            |
| ------------------ | ------------------------------ | ---------------------------------------------------------------------------------- |
| `API_URL`          | API server 位置                | `正式版` https://api.italkutalk.com </br> `測試版` https://api-dev.italkutalk.com    |
| `WEB_URL`          | 此專案的 Web 位置               | `正式版` https://www.italkutalk.com, </br> `測試版` https://web-dev.italkutalk.com  |
| `FB_CLIENT_ID`     | FB 的 iTalkuTalk Client ID     | 568253296977526                                                                    |
| `FB_CLIENT_SECRET` | FB 的 iTalkuTalk Client secret | 43835adc3b2ccaad22583de715e39311                                                   |
| `APPLE_CLIENT_ID`  | Apple 的 iTalkuTalk service ID | `正式版` com.iTalkuTalk.services, </br> `測試版` com.iTalkuTalkDEV.services         |
| `GOOGLE_CLIENT_ID` | GOOGLE 的 iTalkuTalk Client ID | 788138287052-5aj6nqqc8j3onfdi0h00or2a8u3dsu8l.apps.googleusercontent.com           |
| `ECPAY_URL`        | 綠界支付 的 導向URL             | `正式版` https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5, </br> `測試版` https://payment-stage.ecpay.com.tw/Cashier/AioCheckOut/V5        |
| `VERSION`          | Docker Container 的 tag 版本   | `dev-100`, `prod-84`                                                               |

## 使用 Docker 建置及部署

以下為 Docker 建置指令

> Nuxt 專案經實測比較特別，環境變數在 docker run 才取得 env 都取不到(也許有方法，但還沒找到)，只能在 build 階段就設定好，透過 Dockerfile 的 ARG 取得。

```
docker --build-arg API_URL={api_url} \
    --build-arg VERSION={version} \
    -t itut-nuxt-web .
```

以下為 Docker 運行指令

```
docker run -it -p 3000:3000 itut-nuxt-web
```
