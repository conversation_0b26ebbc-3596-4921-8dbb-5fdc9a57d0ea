<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { globalStore } from "../stores/global";

let store = globalStore();
const i18n = useI18n();

// Warning: i18n套件的當下語言只能在vue component中設定
onMounted(() => {
  i18n.locale.value = store.getLanguage;
});

const drawer = ref(false);
const onDrawerExpended = function (e: any) {
  drawer.value = true;
};
const onMenuItemSeleted = function (e: any) {
  drawer.value = false;
};
</script>

<template>
  <div>
    <el-container style="height: 100vh" direction="vertical">
      <el-header>
        <MainHeader @onDrawerExpended="onDrawerExpended" />
      </el-header>
      <el-container>
        <el-main>
          <slot></slot>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<style scoped>
body {
  margin: 0;
}
.el-main {
  background-image: linear-gradient(
    180.99deg,
    #caf0ff 0.85%,
    rgba(108, 210, 251, 0) 115.78%
  );
}
.el-drawer__body {
  margin: 0px;
  padding: 0;
  position: relative;
}
</style>
