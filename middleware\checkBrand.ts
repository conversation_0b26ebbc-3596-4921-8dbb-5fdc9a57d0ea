import { checkBrand } from "~~/api/brand";
import { checkTeacher } from "~~/api/teacher";

import { supportLangs } from "~~/util/language";
import { globalStore } from "../stores/global";
let store = globalStore();

export default defineNuxtRouteMiddleware(async (to, from) => {
  
  console.log(to.name)
  const id = Array.isArray(to.params.id) ? to.params.id[0] : to.params.id;
  let toLang = to.query.language as string;//現在值
  toLang = toLang && supportLangs.includes(toLang) ? toLang : store.getLanguage;//預設值
  to.query.language = toLang;//避免遺失語言參數

  if (to.name === "brandEdit-id") {//進入品牌編輯
    const brandCheck = await checkBrand();
    if (id !== brandCheck.brandID && id !== brandCheck.brandWebIndex)
    {
      to.name = "id";
      return navigateTo(to);
    }
  }

  if (to.name === "teacherEdit-id") {//進入教師編輯
    const teacherCheck = await checkTeacher(id, id);
    console.log(teacherCheck)
    if (id !== teacherCheck.teacherID && id !== teacherCheck.teacherWebIndex)
    {
      to.name = "teacher-id";
      console.log(to)
      return navigateTo(to);
    }
  }
});
