import { User, SystemLanguage, SignIn<PERSON><PERSON><PERSON>, Member } from "../models/user";
import { PaymentCreateResult } from "../models/event";
import axios from "./axios";

enum SignInMethod {
  Facebook = 0,
  Email = 2,
  Apple = 5,
  Google = 6,
}
enum SystemLanguageIndex {
  zh = 1,
  en = 2,
  ja = 3,
  ko = 5,
  vi = 7,
  de = 9,
  fr = 10,
  es = 11,
  ru = 16
}

const runtimeConfig = useRuntimeConfig();
const version = runtimeConfig.public.version;

export const getGuestKey = async (): Promise<string> => {
  return (await axios.get("/api/GetGuestKey")).data.guestKey;
};
export const signInCheck = async (
  guestKey: String,
  email: String,
  password: String,
  language: String
): Promise<SignInCheck> => {
  const platform = { language: language, type: 3 };
  return (
    await axios.post("/api/user/signin/check", {
      guestKey: guestKey,
      kind: SignInMethod.Email,
      email: email,
      password: password,
      appVersion: version,
      platform: platform
    })
  ).data.result as SignInCheck;
};

export const signUp = async (
  guestKey: String,
  email: String,
  password: String,
  language: String
): Promise<Number> => {
  return (
    await axios.post("/api/register/signup", {
      guestKey: guestKey,
      email: email,
      password: password,
      systemLanguage: language,
    })
  ).data.status as Number;
};

export const verify = async (
  guestKey: String,
  email: String,
  code: String,
  language: String
): Promise<User> => {
  const platform = { language: language, type: 3 };
  let user = (await axios.post("/api/register/verify", {
    guestKey: guestKey,
    kind: SignInMethod.Email,
    email: email,
    code: code,
    appVersion: version,
    platform: platform
  })).data.result as User;
  const name = email.split('@')[0];//email的@前的部分
  const profile = await supplyInfo(user.accessKey, user.userID, name);
  user.userPhoto = profile.userPhoto;
  user.userName = profile.userName;
  return user;
};

export const signIn = async (
  guestKey: String,
  email: String,
  password: String,
  language: String
): Promise<User> => {
  const platform = { language: language, type: 3 };
  return (
    await axios.post("/api/user/signin", {
      guestKey: guestKey,
      kind: SignInMethod.Email,
      email: email,
      password: password,
      appVersion: version,
      platform: platform
    })
  ).data.result as User;
};

export const forgot = async (guestKey: String, email: String): Promise<number> => {
  return (
    await axios.post("/api/user/forgot", { guestKey: guestKey, kind: 2, email: email })
  ).data.status as number;
};

export const resetPassword = async (email: String, password: String, code: String): Promise<number> => {
  return (
    await axios.post("/api/user/password/reset", { id: email, password: password, code: code })
  ).data.status as number;
};

export const signInWithToken = async (
  token: String,
): Promise<User> => {
  return (
    await axios.post("/api/user/signin/token", {
      token: token
    })
  ).data.result as User;
};

export const signInWithFb = async (
  guestKey: String,
  uid: String,
  language: String,
  userName: String
): Promise<User> => {
  const platform = { language: language, type: 3 };
  const registed = (await axios.post("/api/user/signin/check", {//檢查是否有註冊過
    guestKey: guestKey,
    kind: SignInMethod.Facebook,
    uid: uid,
    appVersion: version,
    platform: platform
  })).data.result.isExisted as Boolean;
  if (registed) {
    return (
      await axios.post("/api/user/signin", {//註冊過登入
        guestKey: guestKey,
        kind: SignInMethod.Facebook,
        uid: uid,
        appVersion: version,
        platform: platform
      })
    ).data.result as User;
  }
  else {
    let user = (await axios.post("/api/register/verify", {//未註冊註冊
      guestKey: guestKey,
      kind: SignInMethod.Facebook,
      uid: uid,
      email: "",
      appVersion: version,
      platform: platform
    })).data.result as User
    const profile = (await supplyInfo(user.accessKey, user.userID, userName));//設定使用者姓名
    user.userName = profile.userName;
    user.userPhoto = profile.userPhoto;//預設鳥頭
    return user;
  }
};

export const signInWithApple = async (
  guestKey: String,
  uid: String,
  authCode: string,
  language: String,
  email: String,
): Promise<User> => {
  const platform = { language: language, type: 3 };
  const registed = (await axios.post("/api/user/signin/check", {
    guestKey: guestKey,
    kind: SignInMethod.Apple,
    uid: uid,
    appVersion: version,
    apple: {
      authorizationCode: authCode,
      clientID: runtimeConfig.public.appleClientId,
    },
    platform: platform
  })
  ).data.result.isExisted as Boolean;
  console.log(registed);
  if (registed) {
    return (
      await axios.post("/api/user/signin", {//註冊過登入
        guestKey: guestKey,
        kind: SignInMethod.Apple,
        uid: uid,
        appVersion: version,
        apple: {
          authorizationCode: authCode,
          clientID: runtimeConfig.public.appleClientId,
        },
        platform: platform
      })
    ).data.result as User;
  }
  else {
    const userName = email.split('@')[0];//email的@前的部分
    let user = (await axios.post("/api/register/verify", {//未註冊註冊
      guestKey: guestKey,
      kind: SignInMethod.Apple,
      uid: uid,
      email: email,
      userName: userName,
      appVersion: version,
      apple: {
        authorizationCode: authCode,
        clientID: runtimeConfig.public.appleClientId,
      },
      platform: platform
    })).data.result as User;
    const profile = await supplyInfo(user.accessKey, user.userID, userName);
    user.userName = profile.userName;
    user.userPhoto = profile.userPhoto;
    return user;
  }

};

export const signInWithGoogle = async (
  guestKey: String,
  code: String,
  language: String,
): Promise<User> => {
  const platform = { language: language, type: 3 };
    return (
      await axios.post("/api/user/signin/google", {//無論是否註冊過 都用這支
        guestKey: guestKey,
        kind: SignInMethod.Google,
        code: code,
        appVersion: version,
        platform: platform
      })
    ).data.result as User;
};

const supplyInfo = async (accessKey: String, userID: String, name: String) => {//用戶註冊完 需要用這API才能設定名字、預設頭像
  return (await axios.post("/api/register/supplyinfo", { accessKey: accessKey, userID: userID, name: name, photo: "https://itutbox.s3.amazonaws.com/pic/default_personal.jpg" })).data.result;
};

export const logout = async (): Promise<any> => {
  return await axios.post("/api/user/logout", {});
};

export const getUserInfo = async (visitedID: String) => {
  return (await axios.post("/api/user/info", { visitedID: visitedID })).data
    .result;
};

export const setSystemLanguage = async (language: String) => {
  const systemLanguageIndex = SystemLanguageIndex[language as keyof typeof SystemLanguageIndex];
  return (await axios.post("/api/user/systemLanguage", { systemLanguage: language, systemLanguageIndex: systemLanguageIndex, fcmID: "" })).data.result as SystemLanguage;
};

export const deleteAccount = async () => {
  return (await axios.post("/api/user/revoke", {})).data.result;
}

export const checkMember = async (): Promise<Member> => {
  return (await axios.post("/api/user/check/member", {})).data as Member;
}

export const memberPaymentCreate = async (duration: Number): Promise<PaymentCreateResult> => {//通過綠界購買一筆會員 orderType: duration個月
  return (await axios.post("/api/inapppay/order/create/ecpay", { orderType: duration })).data.result as PaymentCreateResult;
};