import { BrandCard, BrandDetail, Brand<PERSON>heck, BrandEdit } from "../models/brand";
import axios from "./axios";
interface brandOptions {//活動相關的API很多有不同Call法，用這樣的方法避免一直重寫export const
  brandID?: string//品牌ID拿來查品牌詳情
  guestKey?: string;//未登入用戶需要用guestKey
  language?: string;//未登入用戶需要提供語言
}

export const getBrandCard = async (options: brandOptions = {}): Promise<BrandCard[]> => {//品牌列表
  const { guestKey, language } = options;
  const payload: any = {};
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;

  return (await axios.post("/api/brand/card", payload)).data.result as BrandCard[];
};
export const getBrandDetail = async (options: brandOptions = {}): Promise<BrandDetail> => {//品牌詳情
  const { guestKey, language, brandID } = options;
  const payload: any = {};
  if (guestKey) payload.guestKey = guestKey;
  if (language) payload.language = language;
  if (brandID) payload.brandID = brandID;
  if (brandID) payload.brandWebIndex = brandID;

  return (await axios.post("/api/brand/detail", payload)).data.result as BrandDetail;
};


export const checkBrand = async (): Promise<BrandCheck> => {//middleWare檢查編輯品牌權限
  return (await axios.post("/api/brand/check", {})).data.result as BrandCheck;
}

export const editBrand = async (brandEdit: BrandEdit) :Promise<number> => {//新增、編輯品牌
  const { brandID, editingLanguage, name, description, icon, photo, brandWebIndex, youtubeID } = brandEdit;
  const response = await axios.post("/api/brand/owner/edit", {
    brandID, editingLanguage, name, description, icon, photo, brandWebIndex, youtubeID
  });
  return response.data.status as number;
}