<script lang="ts" setup>
import { Bonus, QuantityDetail } from "~~/models/promote";
const props = defineProps({
  earning: { type: Object as () => Bonus, default: null },
  quantity: { type: Object as () => QuantityDetail, default: null },
  cardTitle: { type: String, default: "promote.award_sum" },
  cardContent: { type: String, default: "promote.current_promotion_count" }
});
</script>
<template>
  <el-card shadow="always">
    <el-skeleton v-if="earning == null && quantity == null" :rows="5" animated />
    <el-space v-else :fill="true" style="width: 100%" :size="16">
      <div>{{ $t(cardTitle) }}</div>
      <text class="earning-text">{{ props.earning?.exchangeFor }}$ {{ props.earning?.dollar }}</text>
      <!-- TODO: 暫時都用台幣顯示，之後要可以切換貨幣 -->
      <el-progress :percentage="
        props.earning == null ? 0 : ((props.earning?.dollar / 6000) * 100).toFixed(2)
      " />
      <text class="description">{{
        $t(cardContent, {
          count:
            props.quantity == null
              ? 0
              : Object.values(props.quantity).reduce((a, b) => a + b),
        })
      }}</text>
      <PromoteStatisticItem :months="1" :count="props.quantity?.oneMonth">
        <img src="/one-month.png" class="img-content" />
      </PromoteStatisticItem>

      <PromoteStatisticItem :months="6" :count="props.quantity?.sixMonths">
        <img src="/six-month.png" class="img-content" />
      </PromoteStatisticItem>
      <PromoteStatisticItem :months="12" :count="props.quantity?.oneYear">
        <img src="/twelve-month.png" class="img-content" />
      </PromoteStatisticItem>
    </el-space>
  </el-card>
</template>
