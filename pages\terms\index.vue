<script lang="ts" setup>
definePageMeta({
  middleware: ["unauth"],
});
</script>
<template>
  <div class="padding-div">
    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="16">
      <!--第一框顯示帳號資料-->
      <el-space :fill="true" style="width: 100%">
        <div class="terms-info">
          <div class="terms-header">
            <h2>{{ $t('terms.header') }}</h2>
          </div>

          <div class="terms-content">
            <div class="terms-title" style="color: black;">
              <h3>{{ $t('terms.privacy') }}</h3>
            </div>
            <div class="terms-details">
              <p>{{ $t('terms.privacy1') }}</p>
              <p>{{ $t('terms.privacy2') }}</p>
              <p>{{ $t('terms.privacyDetailA1') }}</p>
              <p>{{ $t('terms.privacyDetailA2') }}</p>
              <p>{{ $t('terms.privacyDetailA3') }}</p>
              <p>{{ $t('terms.privacyDetailA4') }}</p>
              <p>{{ $t('terms.privacyDetailB1') }}</p>
              <p>{{ $t('terms.privacyDetailB2') }}</p>
              <p>{{ $t('terms.privacyDetailB3') }}</p>
              <p>{{ $t('terms.privacyDetailB31') }}</p>
              <p>{{ $t('terms.privacyDetailB32') }}</p>
              <p>{{ $t('terms.privacyDetailB33') }}</p>
              <p>{{ $t('terms.privacyDetailB34') }}</p>
              <p>{{ $t('terms.privacyDetailB35') }}</p>
              <p>{{ $t('terms.privacyDetailB4') }}</p>
              <p>{{ $t('terms.privacyDetailB41') }}</p>
              <p>{{ $t('terms.privacyDetailB5') }}</p>
            </div>
            <div class="terms-title" style="color: black;">
              <h3>{{ $t('terms.UGC') }}</h3>
            </div>
            <div class="terms-details">
              <p>{{ $t('terms.UGCDetail') }}</p>
              
            </div>
          </div>
          <AccountDelete></AccountDelete>
        </div>
      </el-space>
    </el-col>
  </div>
</template>
<style scoped>
.terms-info {
  padding: 24px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.terms-header {
  margin-bottom: 10px;
}

.terms-content {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 15px;
  color: #444444;
  margin-bottom: 30px;
}

@media (min-width: 450px) {
  .padding-div {
    padding: 20px;
  }
}
</style>