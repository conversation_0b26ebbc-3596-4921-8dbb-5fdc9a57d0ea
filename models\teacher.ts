
export interface TeacherCard {//老師列表
    teacherID: string;
    photo: string;
    name: string;
    rating: number;
    teacherWebIndex: string;
    nationality: string;//ISO 3166-1 alpha-2
    primaryLanguage: string;//ISO 639-1
    hasEvent: boolean;
}
export interface TeacherDetail {//老師詳情
    teacherID: string;
    brandName: string;
    teacherPhoto: string;
    brandPhoto: string;
    youtubeID: string;
    icon: string;
    teacherName: string;
    rating: number;
    teacherWebIndex: string;
    nationality: string;//ISO 3166-1 alpha-2
    primaryLanguage: string;//ISO 639-1
    transDescription: string;
    oriDescription: string;
    oriLanguage: string;
    eventCount: number;
}

export interface TeacherCheck {//品牌身分驗證-教師
    teacherID: string;
    teacherWebIndex: string;
}

export interface TeacherEdit {//新增、編輯教師
    teacherID: string;
    brandID: string;
    editingLanguage: string;
    name: string;
    description: string;
    nationality: string;
    primaryLanguage: string;
    icon: string;
    photo: string;
    teacherWebIndex: string;
    youtubeID: string;
}