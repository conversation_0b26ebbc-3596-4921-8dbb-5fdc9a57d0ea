<script lang="ts" setup>
import { watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getBrandCard, } from "../../api/brand";
import { getGuestKey } from "../../api/user";
import { globalStore } from "../../stores/global";
import { BrandCard } from "~~/models/brand";
import { User } from "~~/models/user";
const route = useRoute();
const router = useRouter();
const store = globalStore();
const language = ref(route.query.language || 'default');
const loading = ref(true);
const brandCards = ref<BrandCard[]>();
let user: User;
onMounted(async () => {//進入畫面時
  await getBrandList();//取得品牌列表
  loading.value = false;
});

async function getBrandList() {//未登入與已登入有不同的call法
  user = store.getCurrentUser;
  if (user == null)//未登入 傳送gusetKey+language
  {
    console.log("未登入");
    const guestKey = await getGuestKey();
    let languageString = "zh";//預設中文
    if (typeof language.value === 'string') {
      languageString = language.value;
    }
    brandCards.value = await getBrandCard({guestKey: guestKey, language: languageString});
  }
  else//已登入 直接抓系統語言
  {
    console.log("已登入");
    brandCards.value = await getBrandCard();
  }
}

function handleCardClick(brandCard: BrandCard) {//點擊卡片時
  if (brandCard.brandWebIndex)//如果有brandWebIndex 拿來當路由 沒有的話才用ID
  {
    router.push(`/${brandCard.brandWebIndex}?language=${language}`);//原先跳至/brand/[id] 現在改至/[id]
  } else {
    router.push(`/${brandCard.brandID}?language=${language}`);
  }
}
function handleDoNotThing(event: MouseEvent) {//點擊卡片時
  event.stopPropagation();
}
watch(() => route.query.language, async (newLang) => {//語言更新時會重抓資料
  language.value = newLang || 'default';
  //refreshComponent();
  loading.value = true; // 設置加載狀態
  getBrandList(); // 重新獲取品牌列表
  loading.value = false; // 加載完成
});

</script>
<template>
  <el-row>
    <el-col v-for="(brandCard) in brandCards" :key="brandCard.brandID" :xs="24" :sm="12" :md="12" :lg="12" :xl="12"
      class="column-spacing">
      <el-card :body-style="{ padding: '0px' }" class="card" @click="handleCardClick(brandCard)">
        <div class="image-wrapper">
          <img :src="brandCard.photo" class="image" alt="品牌封面" />
        </div>
        <div class="content-wrapper" style="padding: 14px">
          <span>{{ brandCard.name }}</span><span>( {{ brandCard.rating }}</span><span :style="{ color: 'gold' }">★</span><span>)</span>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<style scoped>
.el-col {
  /*兩row之間的空格高度*/
  margin-bottom: 24px;
}

.column-spacing {
  /*column之間的空格寬度*/
  padding: 5px;
}

.card {
  border-radius: 10px;
  /*圓角 */
  overflow: hidden;
  cursor: pointer;
  /*顯示手指 */
  transition: transform 0.2s;
}

.card:hover {
  transform: scale(1.015);
  /* 放大效果 */
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-wrapper {
  width: 100%;
  aspect-ratio: 16 / 9;
  /* 設置圖片的寬高比 */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  width: 100%;
  /* 圖片寬度為容器的 100% */
  height: auto;
  /* 高度自動調整 */
  max-width: 100%;
  /* 最大寬度 100% */
  max-height: 100%;
  /* 最大高度 100% */
  object-fit: cover;
  /* 覆蓋整個容器 */
  display: block;
}

.content-wrapper {
  padding: 14;
  /* 卡片內容的內邊距 */
  font-size: 16px;
  /* 調整字體大小 */
  line-height: 1.5;
  /* 增加行距 */
}

.bold {
  font-weight: bold;
  /* 設置粗體 */
}

.button-container {
  display: flex;
  gap: 5px;
  /* 按鈕之間的間距 */
  margin-top: 10px;
  /* 與上方組件的間距 */
}

.button-container :deep(.el-button + .el-button) {
  margin-left: 0 !important;
  /* 覆蓋 Element Plus 預設的 margin-left */
}
</style>
