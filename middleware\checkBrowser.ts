import { globalStore } from "../stores/global";
import { supportLangs } from "~~/util/language";

let store = globalStore();

export default defineNuxtRouteMiddleware(async (to, from) => {
  if (process.client) {
    const userAgent = navigator.userAgent.toLowerCase();// 檢查是否為 Line 內建瀏覽器
    console.log("瀏覽器：");
    console.log(userAgent);
    const isLineBrowser = userAgent.includes('line');
    // 檢查 URL 中是否包含 ?openExternalBrowser=1
    const urlParams = new URLSearchParams(window.location.search);
    const hasOpenExternalBrowser = urlParams.has('openExternalBrowser');
    if (isLineBrowser) {
      if (!hasOpenExternalBrowser) {
        // 如果是 Line 瀏覽器且 URL 中沒有 openExternalBrowser 參數，則添加它
        urlParams.append('openExternalBrowser', '1');
        const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
        window.location.replace(newUrl);
      }
    } else {
      if (hasOpenExternalBrowser) {
        // 如果不是 Line 瀏覽器且 URL 中有 openExternalBrowser 參數，則移除它
        urlParams.delete('openExternalBrowser');
        const newUrl = `${window.location.pathname}${urlParams.toString() ? '?' + urlParams.toString() : ''}`;
        window.location.replace(newUrl);
      }
    }
  }
});